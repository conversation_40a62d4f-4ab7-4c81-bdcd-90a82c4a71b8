/**
 * 多订单模块 - 响应式样式
 * 包含所有媒体查询和断点适配
 */

/* =================================
   大屏幕 - 4列布局 (1200px+)
   ================================= */
@media (min-width: var(--breakpoint-xl)) {
    .multi-order__order-item {
        flex: 1 1 calc(25% - var(--spacing-3)); /* 4列布局 */
        max-width: calc(25% - var(--spacing-3));
    }
}

/* =================================
   中等屏幕 - 3列布局 (768px-1199px)
   ================================= */
@media (min-width: var(--breakpoint-md)) and (max-width: calc(var(--breakpoint-xl) - 1px)) {
    .multi-order__order-item {
        flex: 1 1 calc(33.333% - var(--spacing-3)); /* 3列布局 */
        max-width: calc(33.333% - var(--spacing-3));
    }
}

/* =================================
   小屏幕 - 2列布局 (480px-767px)
   ================================= */
@media (min-width: var(--breakpoint-sm)) and (max-width: calc(var(--breakpoint-md) - 1px)) {
    .multi-order__order-item {
        flex: 1 1 calc(50% - var(--spacing-2)); /* 2列布局 */
        max-width: calc(50% - var(--spacing-2));
        min-width: 200px;
        padding: var(--spacing-4);
    }

    .multi-order__orders-list {
        padding: var(--spacing-4);
        gap: var(--spacing-3);
    }
}

/* =================================
   超小屏幕 - 2列布局 (479px以下)
   ================================= */
@media (max-width: calc(var(--breakpoint-sm) - 1px)) {
    .multi-order__order-item {
        flex: 1 1 calc(50% - var(--spacing-1)); /* 两列布局 */
        max-width: calc(50% - var(--spacing-1));
        min-width: 140px; /* 确保最小可读宽度 */
        padding: var(--spacing-2); /* 减少内边距以适应小空间 */
        font-size: var(--font-size-xs); /* 整体字体缩小 */
    }

    .multi-order__orders-list {
        padding: var(--spacing-2); /* 减少外边距 */
        gap: var(--spacing-1); /* 减少间距以适应两列 */
        justify-content: flex-start; /* 左对齐 */
    }

    .multi-order__content {
        padding: var(--spacing-3); /* 减少整体内边距 */
    }

    .multi-order__controls {
        flex-direction: column;
        gap: var(--spacing-2);
        padding: var(--spacing-2); /* 减少控制区域内边距 */
    }

    .multi-order__control-btn {
        min-width: auto;
        width: 100%;
        padding: var(--spacing-2) var(--spacing-3); /* 调整按钮内边距 */
        font-size: var(--font-size-xs); /* 缩小按钮字体 */
    }

    /* 订单字段在小屏幕上的优化 */
    .multi-order__order-fields {
        gap: 2px; /* 进一步减少字段间距 */
    }

    .multi-order__order-field {
        font-size: 10px; /* 进一步缩小字段字体 */
        padding: 2px 4px; /* 最小化字段内边距 */
        flex: 1 1 auto;
        min-width: 60px; /* 减少字段最小宽度 */
        line-height: 1.2; /* 紧凑行高 */
    }

    .multi-order__field-label {
        font-size: 10px;
        font-weight: 500; /* 减少字重以节省空间 */
    }

    .multi-order__field-value {
        font-size: 10px;
        line-height: 1.2;
    }

    /* 订单状态在小屏幕上的优化 */
    .multi-order__order-status {
        font-size: 10px;
        margin-top: var(--spacing-1);
    }

    .multi-order__status-label,
    .multi-order__status-value {
        font-size: 10px;
    }

    /* 头部区域优化 */
    .multi-order__header {
        padding: var(--spacing-2);
        font-size: var(--font-size-sm);
    }

    .multi-order__header h3 {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-1);
    }

    .multi-order__order-counter {
        font-size: var(--font-size-xs);
    }
}

/* =================================
   移动端通用优化 (768px以下)
   ================================= */
@media (max-width: var(--breakpoint-md)) {
    .multi-order {
        max-width: 95vw;
        max-height: 85vh;
        margin: 20px;
        min-width: 300px;
    }
    
    .multi-order__content {
        padding: 16px;
    }
    
    .multi-order__controls {
        flex-direction: column;
        gap: 12px;
    }
    
    .multi-order__control-btn {
        width: 100%;
        min-width: auto;
    }
    
    .multi-order__header {
        padding: 12px 16px;
    }
    
    .multi-order__header h3 {
        font-size: 16px;
    }
    
    .multi-order__order-item {
        padding: 12px;
    }

    /* 批量操作栏移动端优化 */
    .multi-order__batch-operations {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-2);
    }

    .multi-order__batch-selection,
    .multi-order__batch-actions {
        justify-content: center;
    }

    .multi-order__batch-btn {
        flex: 1;
        justify-content: center;
    }
}

/* =================================
   极小屏幕优化 (480px以下)
   ================================= */
@media (max-width: var(--breakpoint-sm)) {
    .multi-order {
        min-width: 280px;
        margin: 10px;
    }
    
    .multi-order__order-counter {
        font-size: 10px;
        padding: 2px 8px;
    }

    .multi-order__header {
        padding: var(--spacing-2) var(--spacing-3);
    }

    .multi-order__header h3 {
        font-size: var(--font-size-sm);
    }

    .multi-order__close-btn {
        width: 32px;
        height: 32px;
        font-size: var(--font-size-base);
    }

    /* 状态栏优化 */
    .multi-order__status-bar {
        padding: var(--spacing-2) var(--spacing-3);
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .multi-order__progress-bar {
        width: 100px;
        height: 3px;
    }

    /* 订单操作按钮优化 */
    .multi-order__action-btn {
        width: 24px;
        height: 24px;
        font-size: 10px;
    }

    /* 批量操作进一步优化 */
    .multi-order__batch-btn {
        padding: var(--spacing-1) var(--spacing-2);
        font-size: var(--font-size-xs);
        min-height: 32px;
    }

    .multi-order__selection-count {
        font-size: var(--font-size-xs);
        padding: var(--spacing-1) var(--spacing-2);
    }
}

/* =================================
   横屏模式优化
   ================================= */
@media (max-height: 600px) and (orientation: landscape) {
    .multi-order__header {
        padding: var(--spacing-3) var(--spacing-6);
        min-height: 48px;
    }

    .multi-order__content {
        padding: var(--spacing-4);
    }

    .multi-order__orders-list {
        padding: var(--spacing-3);
    }

    .multi-order__order-item {
        min-height: 100px;
        padding: var(--spacing-3);
    }

    .multi-order__controls {
        padding: var(--spacing-3);
    }

    .multi-order__status-bar {
        padding: var(--spacing-2) var(--spacing-4);
    }
}
