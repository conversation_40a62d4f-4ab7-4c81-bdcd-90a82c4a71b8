/**
 * 多订单模块 - 组件样式
 * 包含订单项、按钮、状态栏等组件样式
 */

/* =================================
   订单项样式 - 流式布局适配
   ================================= */
.multi-order .multi-order__order-item {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-6);
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);

    /* 流式布局 - 响应式宽度 */
    flex: 1 1 var(--multi-order-item-base-width); /* 最小宽度300px，可伸缩 */
    min-width: var(--multi-order-item-min-width); /* 最小宽度限制 */
    max-width: var(--multi-order-item-max-width); /* 最大宽度限制 */
    min-height: var(--multi-order-item-min-height); /* 最小高度保证内容显示 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.multi-order .multi-order__order-item:hover {
    border-color: var(--color-primary);
    box-shadow: var(--shadow-md);
    transform: var(--transform-hover-lift);
    background: var(--bg-card);
}

.multi-order .multi-order__order-item--selected {
    background: var(--color-primary-bg-light);
    border-color: var(--color-primary);
    box-shadow: 0 4px 12px var(--brand-overlay-light);
}

/* 订单项复选框样式 */
.multi-order__order-checkbox {
    display: flex;
    align-items: center;
    margin-right: var(--spacing-2);
}

.multi-order__order-checkbox input[type="checkbox"] {
    width: var(--multi-order-checkbox-size);
    height: var(--multi-order-checkbox-size);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-primary);
    cursor: pointer;
    transition: var(--transition-fast);
    margin: 0;
}

.multi-order__order-checkbox input[type="checkbox"]:checked {
    background: var(--color-primary);
    border-color: var(--color-primary);
}

.multi-order__order-checkbox input[type="checkbox"]:hover {
    border-color: var(--color-primary);
}

.multi-order__order-content {
    flex: 1; /* 占据剩余空间 */
    margin-bottom: var(--spacing-4);
    overflow: hidden;
}

/* =================================
   订单字段流式布局
   ================================= */
.multi-order__order-fields {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
    align-items: flex-start;
    justify-content: flex-start;
}

.multi-order__order-field {
    display: inline-flex;
    align-items: center;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-tight);
    white-space: nowrap;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;

    /* 自适应宽度 */
    flex: 0 1 auto;
    min-width: 0;
}

.multi-order__field-label {
    color: var(--text-secondary);
    font-weight: 600;
    margin-right: var(--spacing-2);
    flex-shrink: 0;
}

.multi-order__field-value {
    color: var(--text-primary);
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
}

/* =================================
   特殊字段样式
   ================================= */

/* 团号字段 */
.multi-order__order-field--group-number {
    background: var(--color-primary-bg-light);
    border-color: var(--color-primary-light);
}

/* 客人字段 */
.multi-order__order-field--guest {
    background: var(--color-success-light);
    border-color: var(--color-success);
}

/* 联系字段 */
.multi-order__order-field--contact {
    background: var(--color-info-light);
    border-color: var(--color-info);
}

/* 时间/日期字段 */
.multi-order__order-field--time,
.multi-order__order-field--date {
    background: var(--color-warning-light);
    border-color: var(--color-warning);
}

/* 路线字段 */
.multi-order__order-field--origin,
.multi-order__order-field--destination,
.multi-order__order-field--route {
    background: var(--color-secondary-light);
    border-color: var(--color-secondary);
}

/* =================================
   订单状态组件
   ================================= */
.multi-order__order-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: auto; /* 推到底部 */
    font-size: var(--font-size-sm);
    flex-shrink: 0; /* 不缩小 */
}

.multi-order__status-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.multi-order__status-value {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-sm);
    font-weight: 600;
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
}

/* =================================
   状态颜色系统
   ================================= */
.multi-order__status-value--detected { 
    background: var(--color-info-light); 
    color: var(--color-info); 
}

.multi-order__status-value--ready { 
    background: var(--color-success-light); 
    color: var(--color-success); 
}

.multi-order__status-value--processing { 
    background: var(--color-warning-light); 
    color: var(--color-warning); 
}

.multi-order__status-value--completed { 
    background: var(--color-success-light); 
    color: var(--color-success); 
}

.multi-order__status-value--error,
.multi-order__status-value--failed { 
    background: var(--color-error-light); 
    color: var(--color-error); 
}

.multi-order__status-value--pending { 
    background: var(--color-gray-200); 
    color: var(--color-gray-600); 
}

/* =================================
   控制按钮区域
   ================================= */
.multi-order__controls {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    padding: var(--spacing-6);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    position: sticky;
    bottom: 0;
}

.multi-order__control-btn {
    padding: var(--spacing-4) var(--spacing-6);
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: 600;
    transition: var(--transition-fast);
    min-width: var(--multi-order-control-btn-min-width);
    font-family: var(--font-family);
}

/* 主要操作按钮 */
.multi-order__control-btn--primary {
    background: var(--color-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.multi-order__control-btn--primary:hover {
    background: var(--color-primary-hover);
    transform: var(--transform-hover-lift);
    box-shadow: var(--shadow-md);
}

/* 次要操作按钮 */
.multi-order__control-btn--secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.multi-order__control-btn--secondary:hover {
    background: var(--bg-card);
    border-color: var(--color-primary);
    transform: var(--transform-hover-small);
}

/* 取消按钮 */
.multi-order__control-btn--cancel {
    background: var(--bg-tertiary);
    color: var(--color-error);
    border: 1px solid var(--color-error-light);
}

.multi-order__control-btn--cancel:hover {
    background: var(--color-error-light);
    transform: var(--transform-hover-small);
}

/* =================================
   批量操作栏样式
   ================================= */
.multi-order__batch-operations {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4) var(--spacing-6);
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    gap: var(--spacing-4);
    flex-wrap: wrap;
    position: sticky;
    top: var(--multi-order-header-height); /* 在头部下方 */
    z-index: var(--z-sticky, 100);
}

.multi-order__batch-selection,
.multi-order__batch-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

.multi-order__batch-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: 500;
    min-height: var(--multi-order-batch-btn-min-height);
    white-space: nowrap;
}

.multi-order__batch-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--color-primary);
    transform: var(--transform-hover-small);
    box-shadow: var(--shadow-sm);
}

.multi-order__batch-btn--primary {
    background: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
}

.multi-order__batch-btn--primary:hover {
    background: var(--color-primary-dark, var(--color-primary));
    border-color: var(--color-primary-dark, var(--color-primary));
}

.multi-order__selection-count {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
    white-space: nowrap;
}

/* =================================
   订单头部和操作
   ================================= */
.multi-order__order-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-3) var(--spacing-4);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.multi-order__order-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    flex: 1;
}

.multi-order__order-number {
    font-weight: 600;
    color: var(--color-primary);
    font-size: var(--font-size-sm);
}

.multi-order__order-id {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-family: monospace;
}

.multi-order__order-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.multi-order__action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: var(--multi-order-action-btn-size);
    height: var(--multi-order-action-btn-size);
    border: none;
    border-radius: var(--radius-sm);
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-xs);
}

.multi-order__action-btn:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
    transform: var(--transform-hover-small);
    box-shadow: var(--shadow-sm);
}

.multi-order__action-btn--edit:hover {
    background: var(--color-primary-light);
    color: var(--color-primary);
}

.multi-order__action-btn--duplicate:hover {
    background: var(--color-info-light);
    color: var(--color-info);
}

.multi-order__action-btn--delete:hover {
    background: var(--color-error-light);
    color: var(--color-error);
}
