/**
 * 多订单模块 - 基础样式
 * 包含容器、布局和基础结构样式
 */

/* =================================
   根容器 - 全屏布局
   ================================= */
.multi-order {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: var(--bg-primary);
    z-index: var(--z-modal, 2000) !important;
    display: flex !important;
    flex-direction: column !important;
    font-family: var(--font-family);
    color: var(--text-primary);
    overflow: hidden !important;
    transform: none !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
}

/* =================================
   头部区域
   ================================= */
.multi-order .multi-order__header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-6) var(--spacing-8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky, 100);
}

.multi-order .multi-order__header h3 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.multi-order .multi-order__header-icon {
    font-size: var(--font-size-2xl);
    color: var(--color-primary);
}

.multi-order .multi-order__header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.multi-order .multi-order__order-counter {
    background: var(--color-primary);
    color: white;
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    min-width: 24px;
    text-align: center;
}

.multi-order .multi-order__close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    color: var(--text-secondary);
}

.multi-order .multi-order__close-btn:hover {
    background: var(--color-error-light);
    color: var(--color-error);
}

/* =================================
   主内容区域
   ================================= */
.multi-order .multi-order__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: var(--spacing-8);
    overflow: hidden;
    background: var(--bg-primary);
}

.multi-order .multi-order__status {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-4) var(--spacing-6);
    margin-bottom: var(--spacing-6);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-align: center;
    border-left: 4px solid var(--color-primary);
}

/* =================================
   订单列表容器 - 流式自动换行布局
   ================================= */
.multi-order .multi-order__orders-list {
    flex: 1;
    overflow-y: auto;
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-6);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);

    /* 流式布局 - 自动换行 */
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    align-content: flex-start;
    justify-content: flex-start;
}

/* 滚动条样式 */
.multi-order .multi-order__orders-list::-webkit-scrollbar {
    width: 8px;
}

.multi-order .multi-order__orders-list::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

.multi-order .multi-order__orders-list::-webkit-scrollbar-thumb {
    background: var(--color-gray-400);
    border-radius: var(--radius-sm);
}

.multi-order .multi-order__orders-list::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500);
}

/* =================================
   状态栏
   ================================= */
.multi-order .multi-order__status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-6);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    position: sticky;
    bottom: 0;
    z-index: var(--z-sticky, 100);
}

.multi-order .multi-order__status-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.multi-order .multi-order__status-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.multi-order .multi-order__progress-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.multi-order .multi-order__progress-bar {
    width: 120px;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.multi-order .multi-order__progress-fill {
    height: 100%;
    background: var(--color-primary);
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
    width: 0%;
}

.multi-order .multi-order__progress-text {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: 500;
}

/* =================================
   处理中状态
   ================================= */
.multi-order--processing {
    pointer-events: none !important;
}

.multi-order--processing .multi-order__status {
    background: var(--color-warning-light, #fff3cd);
    color: var(--color-warning, #856404);
    border-left-color: var(--color-warning, #ffc107);
}
