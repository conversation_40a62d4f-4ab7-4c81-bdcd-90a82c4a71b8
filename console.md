---
type: "manual"
---

script-loader.js:171 ✅ ScriptLoader ready
script-loader.js:128 🔧 Loading phase: infrastructure (6 scripts)
dependency-container.js?v=3.0.0:298 ✅ 依赖容器已初始化
service-locator.js?v=3.0.0:345 ✅ 服务定位器已加载
application-bootstrap.js?v=3.0.0:501 ✅ 应用启动协调器已加载
logger.js?v=3.0.0:222 ✅ Phase complete: infrastructure in 23.8ms
logger.js?v=3.0.0:222 🔧 Loading phase: business-logic (48 scripts)
logger.js?v=3.0.0:222 ✅ ScriptLoader ready
logger.js?v=3.0.0:222 🔧 Loading phase: infrastructure (6 scripts)
logger.js?v=3.0.0:222 ✅ Phase complete: infrastructure in 0.7ms
logger.js?v=3.0.0:222 🔧 Loading phase: business-logic (48 scripts)
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: lifecycleManager
logger.js?v=3.0.0:222 [VersionWatcher] 检测到本地 file 协议，禁用远程版本轮询 (需要通过本地静态服务器运行以启用热版本检测)
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: eventCoordinator
logger.js?v=3.0.0:222 ✅ 特性开关机制已加载
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: vehicleConfigManager
logger.js?v=3.0.0:222 ✅ 车辆配置管理器已加载
logger.js?v=3.0.0:222 ✅ 精简酒店数据已加载 {version: '1.0.0', totalHotels: 57, source: 'essential_inline_data', optimizedFor: 'startup_performance', sizeReduction: '90%', …}
logger.js?v=3.0.0:222 ✅ 酒店数据库已加载 {totalHotels: 4264, metadata: {…}}
logger.js?v=3.0.0:222 ✅ 统一OTA策略配置已加载
logger.js?v=3.0.0:222 ✅ ChannelDetector (子层实现) 已加载
logger.js?v=3.0.0:222 ✅ PromptBuilder (子层实现) 已加载
logger.js?v=3.0.0:222 ✅ GeminiCaller (子层实现) 已加载
logger.js?v=3.0.0:222 ✅ ResultProcessor (子层实现) 已加载
logger.js?v=3.0.0:222 ✅ OrderParser (子层实现) 已加载
logger.js?v=3.0.0:222 ✅ KnowledgeBase (简化版 - 本地数据) 已加载 {version: '3.0.0', architecture: 'simplified_local_data', features: Array(3)}
logger.js?v=3.0.0:222 ✅ 智能地址翻译器已加载 {version: '4.0.0', strategy: '智能本地映射优先 + Gemini AI', features: Array(4), mappings: {…}}
logger.js?v=3.0.0:222 [MultiOrderIndependent] 🧪 调试工具已加载
logger.js?v=3.0.0:222 使用 window.MultiOrderIndependentDebug.showTestUI() 测试UI
logger.js?v=3.0.0:222 ✅ APICaller (子层实现) 已加载
logger.js?v=3.0.0:222 ✅ HistoryManager (子层实现) 已加载
logger.js?v=3.0.0:222 ✅ BusinessFlowController (母层控制器) 已加载
logger.js?v=3.0.0:222 ✅ OrderManagementController (母层控制器) 已加载
logger.js?v=3.0.0:222 ✅ 统一字段映射服务已加载 {version: '1.0.0', strategy: '统一数据契约 + 简化架构', features: Array(5), defaultValues: 10}
logger.js?v=3.0.0:222 🔍 unified-field-mapper 实例验证成功 {hasProcessData: true, hasConvertDataTypes: true, hasApplyDefaultValues: true}
logger.js?v=3.0.0:222 ✅ 简单路由系统已加载
logger.js?v=3.0.0:222 ✅ 页面管理器已加载
logger.js?v=3.0.0:222 🔧 无感知更新配置已加载
logger.js?v=3.0.0:222 🚀 MultiOrder initializing...
logger.js?v=3.0.0:222 ✅ MultiOrder styles attached and verified
logger.js?v=3.0.0:222 ✅ History manager configured
logger.js?v=3.0.0:222 🚀 创建统一的多订单容器
logger.js?v=3.0.0:222 ✅ 统一多订单容器创建成功 (attempt 1)
logger.js?v=3.0.0:222 ✅ MultiOrder container ready and positioned
logger.js?v=3.0.0:222 ✅ MultiOrder global event listeners configured
logger.js?v=3.0.0:222 ✅ Global event listeners configured
logger.js?v=3.0.0:222 ✅ MultiOrder initialized successfully
logger.js?v=3.0.0:222 🎉 MultiOrder auto-initialization completed successfully
logger.js?v=3.0.0:222 ✅ FormManager类定义完成（阶段2: 配置和类定义）
logger.js?v=3.0.0:222 ✅ 权限管理器已加载
logger.js?v=3.0.0:222 🌐 开始初始化统一语言检测器...
logger.js?v=3.0.0:222 ✅ 统一语言检测器初始化成功
logger.js?v=3.0.0:222 ✅ 动画管理器已加载并初始化
logger.js?v=3.0.0:222 ✅ BaseManager适配器已加载，BaseManager全局类已可用
logger.js?v=3.0.0:222 ✅ GeminiServiceAdapter (兼容性适配器) 已加载
logger.js?v=3.0.0:222 ✅ 统一版OTA管理器已加载
logger.js?v=3.0.0:222 ✅ Phase complete: business-logic in 120.8ms
logger.js?v=3.0.0:222 🚀 All scripts loaded in 121.6ms
logger.js?v=3.0.0:222 🚀 开始启动OTA订单处理系统...
logger.js?v=3.0.0:222 🚀 开始启动OTA订单处理系统...
logger.js?v=3.0.0:222 📋 执行启动阶段: dependencies (1/5)
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: appState
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: logger
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: utils
logger.js?v=3.0.0:222 [DependencyContainer] 警告: 服务 eventCoordinator 已存在，将被覆盖
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: eventCoordinator
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: apiService
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: geminiService
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: i18nManager
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: imageUploadManager
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: currencyConverter
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: multiOrderManager
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: orderHistoryManager
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: pagingServiceManager
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: uiManager
logger.js?v=3.0.0:222 [DependencyContainer] 已注册服务: channelDetector
logger.js?v=3.0.0:222 📦 已注册 14 个依赖
logger.js?v=3.0.0:222 📋 执行启动阶段: services (2/5)
logger.js?v=3.0.0:222 [DependencyContainer] 已创建服务实例: appState
logger.js?v=3.0.0:222 ⚙️ 已初始化 6 个核心服务
logger.js?v=3.0.0:222 📋 执行启动阶段: managers (3/5)
logger.js?v=3.0.0:222 🎛️ 已处理 5 个管理器
logger.js?v=3.0.0:222 📋 执行启动阶段: ui (4/5)
logger.js?v=3.0.0:222 ✅ Phase complete: business-logic in 129.2ms
logger.js?v=3.0.0:222 🚀 All scripts loaded in 153.4ms
logger.js?v=3.0.0:222 [UIManager.init] 登录状态: true
logger.js?v=3.0.0:222 [UIManager.showWorkspace] body 添加 logged-in (唯一控制源), class= logged-in
logger.js?v=3.0.0:222 🎨 用户界面初始化完成
logger.js?v=3.0.0:222 📋 执行启动阶段: finalization (5/5)
logger.js?v=3.0.0:222 ✅ 车型配置管理器已稳定运行，无需额外验证
logger.js?v=3.0.0:222 🏁 系统启动完成
logger.js?v=3.0.0:222 ✅ OTA系统启动完成，总耗时: 20.80ms
application-bootstrap.js?v=3.0.0:465 📊 启动报告
logger.js?v=3.0.0:222 ✅ dependencies: 0.80ms
logger.js?v=3.0.0:222    详情: 已注册: appState, 已注册: logger, 已注册: utils, 已注册: eventCoordinator, 已注册: apiService, 已注册: geminiService, 已注册: i18nManager, 已注册: imageUploadManager, 已注册: currencyConverter, 已注册: multiOrderManager, 已注册: orderHistoryManager, 已注册: pagingServiceManager, 已注册: uiManager, 已注册: channelDetector
logger.js?v=3.0.0:222 ✅ services: 0.40ms
logger.js?v=3.0.0:222    详情: 已初始化: appState, 已初始化: logger, 已初始化: utils, 已初始化: eventCoordinator, 已初始化: apiService, 已初始化: geminiService
logger.js?v=3.0.0:222 ✅ managers: 0.20ms
logger.js?v=3.0.0:222    详情: 已初始化: imageUploadManager, 已初始化: currencyConverter, 已初始化: multiOrderManager, 已初始化: orderHistoryManager, 已初始化: pagingServiceManager
logger.js?v=3.0.0:222 ✅ ui: 18.90ms
logger.js?v=3.0.0:222    详情: 国际化管理器已初始化, UI管理器已初始化
logger.js?v=3.0.0:222 ✅ finalization: 0.30ms
logger.js?v=3.0.0:222    详情: 健康检查: 90/100, 全局错误处理已设置, 调试接口已暴露, 字段标准化层已禁用（架构简化）
logger.js?v=3.0.0:222 ✅ OTA系统启动成功，耗时: 20.80ms
logger.js?v=3.0.0:222 🚀 初始化页面系统...
logger.js?v=3.0.0:222 🔒 MultiOrder UI hidden
logger.js?v=3.0.0:222 ✅ 多订单功能已恢复加载
logger.js?v=3.0.0:222 ✅ 页面系统初始化完成
logger.js?v=3.0.0:222 🔧 自动显示工作区...
logger.js?v=3.0.0:222 [UIManager.showWorkspace] body 添加 logged-in (唯一控制源), class= logged-in
logger.js?v=3.0.0:222 [Observer] body.class 变更 => logged-in
logger.js?v=3.0.0:222 [Observer] body.class 变更 => logged-in
realtime-analysis-manager.js?v=3.0.0:415 🔍 多订单数据流追踪 - 第1步：实时分析触发
logger.js?v=3.0.0:222 输入文本长度: 381
logger.js?v=3.0.0:222 输入文本预览: [2025/7/10 17:27] Joshua: 接机：

团号：EJBTBY250712-1
2PAX
13/7 KLIA IN 0100 (MF857) - MOXY PUTRAJAYA
客人：朱芸 
客人联系：18130403306
[2025/7/11 19:22] Joshua: 送机：

团号：EJBTBY250710-1
2PAX
14/7 THE FACE STYLE HOTEL...
logger.js?v=3.0.0:222 🔍 获取到的渠道检测结果: {channel: null, confidence: 0, method: 'no_match'}
realtime-analysis-manager.js?v=3.0.0:449 🔍 多订单数据流追踪 - 第2步：调用Gemini解析
logger.js?v=3.0.0:222 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"isMultiOrder\": true,\n  \"orderCount\": 3,\n  \"orders\": [\n    {\n      \"customer_name\": \"朱芸\",\n      \"customer_contact\": \"18130403306\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250712-1\",\n      \"flight_info\": \"MF857\",\n      \"departure_time\": null,\n      \"arrival_time\": \"01:00\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-13\",\n      \"time\": \"01:00\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"刘凯\",\n      \"customer_contact\": \"18764221412\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250710-1\",\n      \"flight_info\": \"AK5136\",\n      \"departure_time\": \"09:15\",\n      \"arrival_time\": null,\n      \"flight_type\": \"Departure\",\n      \"date\": \"2025-07-14\",\n      \"time\": \"05:00\",\n      \"pickup\": \"The Face Style Hotel KL\",\n      \"destination\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 3,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"顾婉婷 & 苟晓琼\",\n      \"customer_contact\": \"13884407028\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250715\",\n      \"flight_info\": \"AK181\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:15\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-15\",\n      \"time\": \"20:15\",\n      \"pickup\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    }\n  ],\n  \"confidence\": null\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 3006,
    "candidatesTokenCount": 936,
    "totalTokenCount": 3942,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 3006
      }
    ]
  },
  "modelVersion": "gemini-2.5-flash-lite",
  "responseId": "f8-oaInSI6DUz7IP1v2_yQs"
}
logger.js?v=3.0.0:222 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n  \"isMultiOrder\": true,\n  \"orderCount\": 3,\n  \"orders\": [\n    {\n      \"customer_name\": \"朱芸\",\n      \"customer_contact\": \"18130403306\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250712-1\",\n      \"flight_info\": \"MF857\",\n      \"departure_time\": null,\n      \"arrival_time\": \"01:00\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-13\",\n      \"time\": \"01:00\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"刘凯\",\n      \"customer_contact\": \"18764221412\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250710-1\",\n      \"flight_info\": \"AK5136\",\n      \"departure_time\": \"09:15\",\n      \"arrival_time\": null,\n      \"flight_type\": \"Departure\",\n      \"date\": \"2025-07-14\",\n      \"time\": \"05:00\",\n      \"pickup\": \"The Face Style Hotel KL\",\n      \"destination\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 3,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"顾婉婷 & 苟晓琼\",\n      \"customer_contact\": \"13884407028\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250715\",\n      \"flight_info\": \"AK181\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:15\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-15\",\n      \"time\": \"20:15\",\n      \"pickup\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    }\n  ],\n  \"confidence\": null\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
}
logger.js?v=3.0.0:222 [GeminiServiceAdapter] 🔧 业务流控制器原始结果: {type: 'multi-order', order: null, orders: Array(3), channel: null, confidence: 0.8, …}
logger.js?v=3.0.0:222 🚀 unified-field-mapper.processData 开始执行 {customer_name: '朱芸', customer_contact: '18130403306', customer_email: null, ota: null, ota_reference_number: 'EJBTBY250712-1', …}
logger.js?v=3.0.0:222 🚀 unified-field-mapper.processData 复制数据完成 {customer_name: '朱芸', customer_contact: '18130403306', customer_email: null, ota: null, ota_reference_number: 'EJBTBY250712-1', …}
logger.js?v=3.0.0:222 🚀 unified-field-mapper.processData 最终返回数据 {customer_name: '朱芸', customer_contact: '18130403306', customer_email: null, ota: null, ota_reference_number: 'EJBTBY250712-1', …}
logger.js?v=3.0.0:222 🚀 unified-field-mapper.processData 开始执行 {customer_name: '刘凯', customer_contact: '18764221412', customer_email: null, ota: null, ota_reference_number: 'EJBTBY250710-1', …}
logger.js?v=3.0.0:222 🚀 unified-field-mapper.processData 复制数据完成 {customer_name: '刘凯', customer_contact: '18764221412', customer_email: null, ota: null, ota_reference_number: 'EJBTBY250710-1', …}
logger.js?v=3.0.0:222 🚀 unified-field-mapper.processData 最终返回数据 {customer_name: '刘凯', customer_contact: '18764221412', customer_email: null, ota: null, ota_reference_number: 'EJBTBY250710-1', …}
logger.js?v=3.0.0:222 🚀 unified-field-mapper.processData 开始执行 {customer_name: '顾婉婷 & 苟晓琼', customer_contact: '13884407028', customer_email: null, ota: null, ota_reference_number: 'EJBTBY250715', …}
logger.js?v=3.0.0:222 🚀 unified-field-mapper.processData 复制数据完成 {customer_name: '顾婉婷 & 苟晓琼', customer_contact: '13884407028', customer_email: null, ota: null, ota_reference_number: 'EJBTBY250715', …}
logger.js?v=3.0.0:222 🚀 unified-field-mapper.processData 最终返回数据 {customer_name: '顾婉婷 & 苟晓琼', customer_contact: '13884407028', customer_email: null, ota: null, ota_reference_number: 'EJBTBY250715', …}
gemini-service-adapter.js?v=3.0.0:230 🤖 Gemini parseOrder 返回
logger.js?v=3.0.0:222 meta: {isRealtime: true}
logger.js?v=3.0.0:222 data (object): (3) [{…}, {…}, {…}]
logger.js?v=3.0.0:222 data (json):
[
  {
    "customer_name": "朱芸",
    "customer_contact": "18130403306",
    "customer_email": null,
    "ota": null,
    "ota_reference_number": "EJBTBY250712-1",
    "flight_info": "MF857",
    "departure_time": null,
    "arrival_time": "01:00",
    "flight_type": "Arrival",
    "pickup_date": null,
    "pickup_time": null,
    "pickup_location": null,
    "dropoff_location": null,
    "passenger_count": null,
    "luggage_count": null,
    "sub_category_id": 2,
    "car_type_id": null,
    "driving_region_id": 1,
    "baby_chair": false,
    "tour_guide": false,
    "meet_and_greet": false,
    "needs_paging_service": false,
    "ota_price": 0,
    "currency": null,
    "extra_requirement": null,
    "price_calculation": null,
    "date": "2025-07-13",
    "time": "01:00",
    "pickup": "Kuala Lumpur International Airport (KLIA1)",
    "destination": "Moxy Putrajaya",
    "passenger_number": 2,
    "luggage_number": 0,
    "confidence": 0.8,
    "orderIndex": 0,
    "processedAt": "2025-08-22T20:13:50.919Z",
    "languages_id_array": {
      "0": "2"
    },
    "incharge_by_backend_user_id": 310
  },
  {
    "customer_name": "刘凯",
    "customer_contact": "18764221412",
    "customer_email": null,
    "ota": null,
    "ota_reference_number": "EJBTBY250710-1",
    "flight_info": "AK5136",
    "departure_time": "09:15",
    "arrival_time": null,
    "flight_type": "Departure",
    "pickup_date": null,
    "pickup_time": null,
    "pickup_location": null,
    "dropoff_location": null,
    "passenger_count": null,
    "luggage_count": null,
    "sub_category_id": 3,
    "car_type_id": null,
    "driving_region_id": 1,
    "baby_chair": false,
    "tour_guide": false,
    "meet_and_greet": false,
    "needs_paging_service": false,
    "ota_price": 0,
    "currency": null,
    "extra_requirement": null,
    "price_calculation": null,
    "date": "2025-07-14",
    "time": "05:00",
    "pickup": "The Face Style Hotel KL",
    "destination": "Kuala Lumpur International Airport 2 (KLIA2)",
    "passenger_number": 2,
    "luggage_number": 0,
    "confidence": 0.8,
    "orderIndex": 1,
    "processedAt": "2025-08-22T20:13:50.919Z",
    "languages_id_array": "[Circular]",
    "incharge_by_backend_user_id": 310
  },
  {
    "customer_name": "顾婉婷 & 苟晓琼",
    "customer_contact": "13884407028",
    "customer_email": null,
    "ota": null,
    "ota_reference_number": "EJBTBY250715",
    "flight_info": "AK181",
    "departure_time": null,
    "arrival_time": "20:15",
    "flight_type": "Arrival",
    "pickup_date": null,
    "pickup_time": null,
    "pickup_location": null,
    "dropoff_location": null,
    "passenger_count": null,
    "luggage_count": null,
    "sub_category_id": 2,
    "car_type_id": null,
    "driving_region_id": 1,
    "baby_chair": false,
    "tour_guide": false,
    "meet_and_greet": false,
    "needs_paging_service": false,
    "ota_price": 0,
    "currency": null,
    "extra_requirement": null,
    "price_calculation": null,
    "date": "2025-07-15",
    "time": "20:15",
    "pickup": "Kuala Lumpur International Airport 2 (KLIA2)",
    "destination": "Moxy Putrajaya",
    "passenger_number": 2,
    "luggage_number": 0,
    "confidence": 0.8,
    "orderIndex": 2,
    "processedAt": "2025-08-22T20:13:50.919Z",
    "languages_id_array": "[Circular]",
    "incharge_by_backend_user_id": 310
  }
]
logger.js?v=3.0.0:227 [MultiOrderIndependent][INFO] 开始初始化独立多订单模组
logger.js?v=3.0.0:227 [GoMyHireAPI][INFO] 独立GoMyHire API客户端初始化完成
logger.js?v=3.0.0:227 [GeminiDataProcessor][INFO] Gemini数据处理器初始化完成
logger.js?v=3.0.0:227 [StaticDataSyncer][INFO] 静态数据同步器初始化完成
logger.js?v=3.0.0:227 [HistoryIntegrator][INFO] 历史存储集成器初始化完成
logger.js?v=3.0.0:227 [IndependentUIRenderer][INFO] 独立UI渲染器初始化完成
logger.js?v=3.0.0:227 [MultiOrderIndependent][INFO] 核心组件初始化完成
logger.js?v=3.0.0:227 [StaticDataSyncer][INFO] 开始同步静态数据
logger.js?v=3.0.0:227 [StaticDataSyncer][INFO] 获取车型数据: 18条
logger.js?v=3.0.0:222 Gemini parseResult: (3) [{…}, {…}, {…}]
logger.js?v=3.0.0:222 🔍 MultiOrder: Received multi-order detection event {orderCount: 3, confidence: 30}
logger.js?v=3.0.0:232 Container not available, attempting to re-acquire...
console.warn @ logger.js?v=3.0.0:232
showUI @ multi-order.js?v=3.0.0:168
(anonymous) @ multi-order.js?v=3.0.0:1307
fallbackToLegacyMultiOrder @ realtime-analysis-manager.js?v=3.0.0:963
navigateToMultiOrderPageV2 @ realtime-analysis-manager.js?v=3.0.0:920
triggerRealtimeAnalysis @ realtime-analysis-manager.js?v=3.0.0:500
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js?v=3.0.0:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js?v=3.0.0:402
(anonymous) @ realtime-analysis-manager.js?v=3.0.0:244
setTimeout
(anonymous) @ realtime-analysis-manager.js?v=3.0.0:238
logger.js?v=3.0.0:222 ✅ Container re-acquired successfully
logger.js?v=3.0.0:222 🔧 检测到简单订单数据，使用详细测试数据增强显示效果
logger.js?v=3.0.0:222 ✅ 显示统一多订单容器
logger.js?v=3.0.0:222 🎯 MultiOrder 统一模式显示: {containerClass: 'multi-order', zIndex: '10002', display: 'flex'}
logger.js?v=3.0.0:227 [StaticDataSyncer][INFO] 获取服务类型数据: 3条
logger.js?v=3.0.0:222 MultiOrder status: 检测到 3 个订单，置信度: 3000%
logger.js?v=3.0.0:227 [StaticDataSyncer][INFO] 获取区域数据: 11条
logger.js?v=3.0.0:227 [StaticDataSyncer][INFO] 获取语言数据: 11条
logger.js?v=3.0.0:227 [StaticDataSyncer][INFO] 获取后端用户数据: 44条
logger.js?v=3.0.0:227 [StaticDataSyncer][INFO] 静态数据同步完成 {carTypes: 18, serviceTypes: 3, regions: 11, languages: 11, backendUsers: 44}
logger.js?v=3.0.0:227 [MultiOrderIndependent][INFO] 静态数据同步完成 {carTypes: 18, serviceTypes: 3, regions: 11, languages: 11}
logger.js?v=3.0.0:227 [MultiOrderIndependent][INFO] 独立多订单模组初始化完成
logger.js?v=3.0.0:227 [MultiOrderIndependent][INFO] 按需初始化完成
logger.js?v=3.0.0:222 编辑字段: {orderId: 'order_2', fieldName: '时间'}
logger.js?v=3.0.0:222 Updated order field: {orderId: 'order_2', field: 'pickupTime', value: '2024-01-15 16:00'}
logger.js?v=3.0.0:222 MultiOrder status: 已更新 时间: 2024-01-15 16:00
