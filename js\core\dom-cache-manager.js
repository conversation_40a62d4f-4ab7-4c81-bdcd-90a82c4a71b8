/**
 * DOM缓存管理器 - 性能优化核心组件
 * 解决重复DOM查询问题，提升渲染性能
 * 
 * @description 提供全局DOM元素缓存机制，减少getElementById等操作的开销
 * @performance 预期性能提升：
 *   - 减少DOM查询时间 60-80%
 *   - 降低重绘回流频率
 *   - 提升交互响应性
 */

window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * DOM缓存管理器
     * 提供高效的DOM元素缓存和管理机制
     */
    class DOMCacheManager {
        constructor() {
            this.cache = new Map();
            this.observers = new Map();
            this.stats = {
                hits: 0,
                misses: 0,
                invalidations: 0
            };
        }

        /**
         * 获取DOM元素（带缓存）
         * @param {string} id - 元素ID
         * @param {boolean} forceRefresh - 是否强制刷新缓存
         * @returns {HTMLElement|null} DOM元素
         */
        get(id, forceRefresh = false) {
            // 强制刷新或缓存不存在时，重新查询
            if (forceRefresh || !this.cache.has(id)) {
                const element = document.getElementById(id);
                if (element) {
                    this.cache.set(id, element);
                    this.setupElementObserver(id, element);
                    this.stats.misses++;
                } else {
                    // 如果元素不存在，缓存null避免重复查询
                    this.cache.set(id, null);
                    this.stats.misses++;
                    return null;
                }
            } else {
                this.stats.hits++;
            }

            return this.cache.get(id);
        }

        /**
         * 批量获取DOM元素
         * @param {string[]} ids - 元素ID数组
         * @returns {Object} 元素对象映射
         */
        getMultiple(ids) {
            const elements = {};
            ids.forEach(id => {
                elements[id] = this.get(id);
            });
            return elements;
        }

        /**
         * 设置元素观察器，监听DOM变化
         * @param {string} id - 元素ID
         * @param {HTMLElement} element - DOM元素
         */
        setupElementObserver(id, element) {
            if (this.observers.has(id)) {
                return; // 已存在观察器
            }

            // 创建MutationObserver监听元素移除
            const observer = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    if (mutation.type === 'childList') {
                        mutation.removedNodes.forEach(node => {
                            if (node === element || (node.nodeType === 1 && node.contains(element))) {
                                this.invalidate(id);
                            }
                        });
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            this.observers.set(id, observer);
        }

        /**
         * 使缓存失效
         * @param {string} id - 元素ID
         */
        invalidate(id) {
            if (this.cache.has(id)) {
                this.cache.delete(id);
                this.stats.invalidations++;
            }

            // 清理观察器
            if (this.observers.has(id)) {
                this.observers.get(id).disconnect();
                this.observers.delete(id);
            }
        }

        /**
         * 清空所有缓存
         */
        clear() {
            this.cache.clear();
            
            // 断开所有观察器
            this.observers.forEach(observer => observer.disconnect());
            this.observers.clear();
            
            this.stats.invalidations++;
        }

        /**
         * 预加载常用DOM元素
         */
        preload() {
            const commonElements = [
                'app', 'workspace', 'loginPanel',
                'orderInput', 'customerName', 'customerContact', 'customerEmail',
                'flightInfo', 'otaPrice', 'otaReferenceNumber',
                'pickupDate', 'pickupTime', 'passengerCount', 'luggageCount',
                'subCategoryId', 'carTypeId', 'drivingRegionId',
                'extraRequirement', 'destination'
            ];

            // 批量预加载
            commonElements.forEach(id => {
                this.get(id);
            });

        }

        /**
         * 获取缓存统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            const total = this.stats.hits + this.stats.misses;
            return {
                ...this.stats,
                total,
                hitRate: total > 0 ? (this.stats.hits / total * 100).toFixed(2) + '%' : '0%',
                cacheSize: this.cache.size
            };
        }

        /**
         * 优化缓存 - 清理无效元素
         */
        optimize() {
            const toRemove = [];
            
            this.cache.forEach((element, id) => {
                if (element && !document.contains(element)) {
                    toRemove.push(id);
                }
            });

            toRemove.forEach(id => {
                this.invalidate(id);
            });

        }
    }

    // 创建全局DOM缓存管理器实例
    const domCache = new DOMCacheManager();

    // 暴露到全局
    window.OTA.domCache = domCache;

    // 提供便捷的全局函数
    window.getDOMElement = (id, forceRefresh = false) => domCache.get(id, forceRefresh);
    window.getDOMElements = (ids) => domCache.getMultiple(ids);

    // DOM加载完成后预加载常用元素
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            domCache.preload();
        });
    } else {
        domCache.preload();
    }

    // 定期优化缓存（每30秒）
    setInterval(() => {
        domCache.optimize();
    }, 30000);


})();