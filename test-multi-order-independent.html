<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单独立模组测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #667eea;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 13px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 12px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.warn {
            color: #ffc107;
        }
        .log-entry.info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <h1>🔢 多订单独立模组测试</h1>

    <div class="test-container">
        <h2 class="test-title">📊 模组状态检查</h2>
        <button class="btn" onclick="checkModuleStatus()">检查模组状态</button>
        <button class="btn btn-secondary" onclick="initializeModule()">初始化模组</button>
        <div id="status-display"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🧪 功能测试</h2>
        <button class="btn" onclick="testSimpleUI()">测试UI显示 (3个订单)</button>
        <button class="btn" onclick="testLargeUI()">测试大量订单 (10个订单)</button>
        <button class="btn" onclick="testMaxUI()">测试最大限制 (30个订单)</button>
        <button class="btn btn-secondary" onclick="testOverLimit()">测试超限 (35个订单)</button>
    </div>

    <div class="test-container">
        <h2 class="test-title">📝 日志查看</h2>
        <button class="btn" onclick="showLogs('all')">显示所有日志</button>
        <button class="btn" onclick="showLogs('api')">API日志</button>
        <button class="btn" onclick="showLogs('data')">数据处理日志</button>
        <button class="btn btn-secondary" onclick="clearLogs()">清空日志</button>
        <div id="log-display" class="log-container" style="display: none;"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🔧 调试工具</h2>
        <button class="btn" onclick="simulateMultiOrderEvent()">模拟多订单事件</button>
        <button class="btn" onclick="testAPICall()">测试API调用</button>
        <button class="btn btn-secondary" onclick="resetModule()">重置模组</button>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/modules/multi-order-independent.js"></script>

    <script>
        // 测试脚本
        let testLogger = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testLogger.push({ timestamp, message, type });
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function displayStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function checkModuleStatus() {
            try {
                if (typeof window.MultiOrderIndependent === 'undefined') {
                    displayStatus('❌ 模组未加载', 'error');
                    return;
                }

                const state = window.MultiOrderIndependent.getState();
                const statusText = `
                    ✅ 模组已加载<br>
                    📊 初始化状态: ${state.initialized ? '✅ 已初始化' : '❌ 未初始化'}<br>
                    📦 订单数量: ${state.orderCount}<br>
                    📋 静态数据: ${state.hasStaticData ? '✅ 已加载' : '❌ 未加载'}<br>
                    🎯 选中订单: ${state.selectedCount || 0}<br>
                    📈 API统计: ${state.apiStats ? `${state.apiStats.successRate} 成功率` : '无数据'}
                `;
                displayStatus(statusText, 'success');
                log('模组状态检查完成', 'info');

            } catch (error) {
                displayStatus(`❌ 状态检查失败: ${error.message}`, 'error');
                log(`状态检查失败: ${error.message}`, 'error');
            }
        }

        async function initializeModule() {
            try {
                displayStatus('🔄 正在初始化模组...', 'info');
                const success = await window.MultiOrderIndependent.initialize();
                
                if (success) {
                    displayStatus('✅ 模组初始化成功', 'success');
                    log('模组初始化成功', 'info');
                } else {
                    displayStatus('❌ 模组初始化失败', 'error');
                    log('模组初始化失败', 'error');
                }
            } catch (error) {
                displayStatus(`❌ 初始化异常: ${error.message}`, 'error');
                log(`初始化异常: ${error.message}`, 'error');
            }
        }

        function createMockOrders(count) {
            const orders = [];
            for (let i = 1; i <= count; i++) {
                orders.push({
                    id: `test_order_${i}`,
                    customer_name: `测试客户${i}`,
                    customer_contact: `1380000000${i}`,
                    pickup: `上车地点${i}`,
                    destination: `目的地${i}`,
                    notes: `测试订单${i}`,
                    selected: true,
                    status: 'ready'
                });
            }
            return orders;
        }

        async function testSimpleUI() {
            try {
                await ensureInitialized();
                const orders = createMockOrders(3);
                window.MultiOrderIndependent.showUI(orders);
                log('简单UI测试启动', 'info');
            } catch (error) {
                displayStatus(`❌ UI测试失败: ${error.message}`, 'error');
                log(`UI测试失败: ${error.message}`, 'error');
            }
        }

        async function testLargeUI() {
            try {
                await ensureInitialized();
                const orders = createMockOrders(10);
                window.MultiOrderIndependent.showUI(orders);
                log('大量订单UI测试启动', 'info');
            } catch (error) {
                displayStatus(`❌ 大量订单测试失败: ${error.message}`, 'error');
                log(`大量订单测试失败: ${error.message}`, 'error');
            }
        }

        async function testMaxUI() {
            try {
                await ensureInitialized();
                const orders = createMockOrders(30);
                window.MultiOrderIndependent.showUI(orders);
                log('最大限制UI测试启动', 'info');
            } catch (error) {
                displayStatus(`❌ 最大限制测试失败: ${error.message}`, 'error');
                log(`最大限制测试失败: ${error.message}`, 'error');
            }
        }

        async function testOverLimit() {
            try {
                await ensureInitialized();
                const orders = createMockOrders(35);
                window.MultiOrderIndependent.showUI(orders);
                log('超限测试启动', 'info');
            } catch (error) {
                displayStatus(`✅ 超限保护正常: ${error.message}`, 'success');
                log(`超限保护正常: ${error.message}`, 'info');
            }
        }

        function simulateMultiOrderEvent() {
            if (window.MultiOrderIndependentDebug) {
                window.MultiOrderIndependentDebug.simulateMultiOrderDetection(5);
                log('多订单事件模拟完成', 'info');
            } else {
                displayStatus('❌ 调试工具不可用', 'error');
            }
        }

        async function ensureInitialized() {
            const state = window.MultiOrderIndependent.getState();
            if (!state.initialized) {
                await window.MultiOrderIndependent.initialize();
            }
        }

        function showLogs(module) {
            try {
                const logDiv = document.getElementById('log-display');
                let logs = [];

                if (module === 'all') {
                    logs = window.MultiOrderIndependent.getLogs();
                } else {
                    logs = window.MultiOrderIndependent.getLogs(module);
                }

                if (logs.length === 0) {
                    logDiv.innerHTML = '<div class="log-entry">暂无日志</div>';
                } else {
                    logDiv.innerHTML = logs.map(log => 
                        `<div class="log-entry ${log.level.toLowerCase()}">
                            [${log.timestamp}] [${log.module}] ${log.message}
                            ${log.data ? '<br>' + JSON.stringify(log.data, null, 2) : ''}
                        </div>`
                    ).join('');
                }

                logDiv.style.display = 'block';
                log(`显示${module}日志，共${logs.length}条`, 'info');

            } catch (error) {
                displayStatus(`❌ 日志显示失败: ${error.message}`, 'error');
            }
        }

        function clearLogs() {
            document.getElementById('log-display').style.display = 'none';
            testLogger = [];
            log('日志已清空', 'info');
        }

        function resetModule() {
            try {
                window.MultiOrderIndependent.destroy();
                displayStatus('✅ 模组已重置', 'success');
                log('模组重置完成', 'info');
            } catch (error) {
                displayStatus(`❌ 重置失败: ${error.message}`, 'error');
                log(`重置失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动检查状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkModuleStatus();
            }, 1000);
        });
    </script>
</body>
</html>
