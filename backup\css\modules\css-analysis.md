# 多订单模组CSS规则分析

## 总体概况
- **总行数**: 约1120行CSS代码（从1261行到2374行）
- **!important使用**: 几乎每个属性都使用了!important
- **响应式断点**: 多个重复的媒体查询
- **主要问题**: 样式与逻辑混合、大量重复、优先级混乱

## CSS规则分类

### 1. 基础容器和布局样式 (multi-order-base.css)
**行数范围**: 1265-1403
**主要类名**:
- `.multi-order-unified-container` - 根容器
- `.multi-order-header` - 头部区域
- `.multi-order-content` - 内容区域
- `.orders-list` - 订单列表容器
- `.status` - 状态信息

**特点**:
- 全屏固定定位布局
- Flexbox布局系统
- 滚动条样式定制

### 2. 组件样式 (multi-order-components.css)
**行数范围**: 1424-1863
**主要类名**:
- `.order-item` - 订单项
- `.order-fields` - 订单字段容器
- `.order-field` - 单个字段
- `.controls` - 控制按钮区域
- `.batch-operations-bar` - 批量操作栏
- `.status-bar` - 状态栏

**特点**:
- 流式布局适配
- 交互状态样式
- 特殊字段颜色标识

### 3. 响应式样式 (multi-order-responsive.css)
**行数范围**: 2199-2373
**断点定义**:
- `@media (min-width: 1200px)` - 大屏幕4列布局
- `@media (min-width: 768px) and (max-width: 1199px)` - 中屏3列布局
- `@media (min-width: 480px) and (max-width: 767px)` - 小屏2列布局
- `@media (max-width: 479px)` - 超小屏2列布局
- `@media (max-width: 768px)` - 移动端优化
- `@media (max-width: 480px)` - 极小屏优化

**问题**:
- 断点重复定义
- 相同断点的规则分散

### 4. 主题样式 (multi-order-themes.css)
**行数范围**: 分散在各处
**主要规则**:
- `[data-theme="dark"]` 相关样式
- 状态颜色系统
- 暗色主题适配

## BEM命名规范映射

| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.multi-order-unified-container` | `.multi-order` | 根容器 |
| `.multi-order-content` | `.multi-order__content` | 内容区域 |
| `.orders-list` | `.multi-order__orders-list` | 订单列表 |
| `.order-item` | `.multi-order__order-item` | 订单项 |
| `.order-header` | `.multi-order__order-header` | 订单头部 |
| `.order-fields` | `.multi-order__order-fields` | 订单字段容器 |
| `.order-field` | `.multi-order__order-field` | 单个订单字段 |
| `.controls` | `.multi-order__controls` | 控制按钮区域 |
| `.status-bar` | `.multi-order__status-bar` | 状态栏 |
| `.batch-operations-bar` | `.multi-order__batch-operations` | 批量操作栏 |

## !important使用分析

### 必要的!important (需保留)
- 覆盖第三方库样式的规则
- 确保模态框z-index优先级的规则
- 关键布局属性（position: fixed等）

### 可移除的!important (约90%)
- 大部分颜色、间距、字体样式
- 可通过提高CSS特异性解决的规则
- 内部组件样式

## CSS变量使用情况

### 已使用的变量
- `var(--bg-primary)`, `var(--bg-secondary)`, `var(--bg-tertiary)`
- `var(--text-primary)`, `var(--text-secondary)`
- `var(--color-primary)`, `var(--color-success)` 等
- `var(--spacing-*)`, `var(--radius-*)`, `var(--font-size-*)`

### 硬编码值需替换
- 固定像素值: `10px`, `16px`, `20px` 等
- 硬编码颜色: `#ccc`, `#bbb`, `rgba(0,0,0,0.5)` 等
- 固定尺寸: `120px`, `300px`, `450px` 等

## 重复规则统计

### 响应式断点重复
- `@media (max-width: 768px)` 出现3次
- `@media (max-width: 480px)` 出现2次
- 相同属性在不同断点重复定义

### 样式规则重复
- 按钮样式重复定义
- 间距和颜色重复设置
- 字体大小重复声明

## 优化建议

1. **移除90%的!important声明**
2. **合并重复的媒体查询**
3. **统一使用CSS变量**
4. **建立BEM命名规范**
5. **分离样式到独立文件**
6. **优化CSS选择器特异性**
