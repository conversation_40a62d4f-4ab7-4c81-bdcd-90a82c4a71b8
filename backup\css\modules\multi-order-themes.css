/**
 * 多订单模块 - 主题样式
 * 包含暗色主题和其他主题变体
 */

/* =================================
   暗色主题
   ================================= */
[data-theme="dark"] .multi-order__close-btn {
    color: #ccc;
}

[data-theme="dark"] .multi-order__close-btn:hover {
    background: rgba(255,255,255,0.1);
}

[data-theme="dark"] .multi-order__status-label {
    color: #bbb;
}

[data-theme="dark"] .multi-order__status-value {
    background: #444;
    color: #e0e0e0;
}

[data-theme="dark"] .multi-order__order-counter {
    background: var(--color-primary, #0d6efd);
}

/* 暗色主题状态颜色 */
[data-theme="dark"] .multi-order__status-value--ready {
    background: rgba(40, 167, 69, 0.2);
    color: #90ee90;
}

[data-theme="dark"] .multi-order__status-value--processing {
    background: rgba(255, 193, 7, 0.2);
    color: #ffecb3;
}

[data-theme="dark"] .multi-order__status-value--completed {
    background: rgba(0, 123, 255, 0.2);
    color: #90caf9;
}

[data-theme="dark"] .multi-order__status-value--error,
[data-theme="dark"] .multi-order__status-value--failed {
    background: rgba(220, 53, 69, 0.2);
    color: #ffcccb;
}

/* 暗色主题处理中状态 */
[data-theme="dark"] .multi-order--processing .multi-order__status {
    background: rgba(255, 193, 7, 0.1);
    color: #ffecb3;
}

/* =================================
   高对比度主题支持
   ================================= */
@media (prefers-contrast: high) {
    .multi-order {
        border: 2px solid var(--text-primary);
    }

    .multi-order__order-item {
        border: 2px solid var(--text-primary);
    }

    .multi-order__order-item--selected {
        border: 3px solid var(--color-primary);
        background: var(--color-primary);
        color: white;
    }

    .multi-order__order-field {
        border: 1px solid var(--text-primary);
    }

    .multi-order__control-btn {
        border: 2px solid var(--text-primary);
    }

    .multi-order__batch-btn {
        border: 2px solid var(--text-primary);
    }
}

/* =================================
   减少动画主题支持
   ================================= */
@media (prefers-reduced-motion: reduce) {
    .multi-order * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .multi-order__order-item:hover {
        transform: none;
    }

    .multi-order__control-btn:hover {
        transform: none;
    }

    .multi-order__action-btn:hover {
        transform: none;
    }

    .multi-order__batch-btn:hover {
        transform: none;
    }
}

/* =================================
   打印样式
   ================================= */
@media print {
    .multi-order {
        position: static !important;
        width: 100% !important;
        height: auto !important;
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .multi-order__header {
        background: white !important;
        color: black !important;
        border-bottom: 2px solid black !important;
    }

    .multi-order__close-btn,
    .multi-order__controls,
    .multi-order__batch-operations,
    .multi-order__action-btn {
        display: none !important;
    }

    .multi-order__order-item {
        background: white !important;
        border: 1px solid black !important;
        box-shadow: none !important;
        break-inside: avoid;
        margin-bottom: 10px;
    }

    .multi-order__order-field {
        background: white !important;
        border: 1px solid black !important;
        color: black !important;
    }

    .multi-order__status-value {
        background: white !important;
        border: 1px solid black !important;
        color: black !important;
    }

    .multi-order__orders-list {
        background: white !important;
        border: none !important;
        display: block !important;
    }

    .multi-order__order-item {
        display: block !important;
        width: 100% !important;
        max-width: none !important;
        flex: none !important;
    }
}

/* =================================
   自定义主题变量覆盖
   ================================= */

/* 蓝色主题 */
[data-theme="blue"] .multi-order {
    --color-primary: #007bff;
    --color-primary-hover: #0056b3;
    --color-primary-light: #b3d7ff;
    --color-primary-bg-light: rgba(0, 123, 255, 0.1);
}

/* 绿色主题 */
[data-theme="green"] .multi-order {
    --color-primary: #28a745;
    --color-primary-hover: #1e7e34;
    --color-primary-light: #b3e5c7;
    --color-primary-bg-light: rgba(40, 167, 69, 0.1);
}

/* 橙色主题 */
[data-theme="orange"] .multi-order {
    --color-primary: #fd7e14;
    --color-primary-hover: #e8590c;
    --color-primary-light: #ffd3a5;
    --color-primary-bg-light: rgba(253, 126, 20, 0.1);
}

/* 红色主题 */
[data-theme="red"] .multi-order {
    --color-primary: #dc3545;
    --color-primary-hover: #c82333;
    --color-primary-light: #f5c6cb;
    --color-primary-bg-light: rgba(220, 53, 69, 0.1);
}

/* =================================
   季节性主题
   ================================= */

/* 春季主题 */
[data-theme="spring"] .multi-order {
    --color-primary: #32cd32;
    --bg-primary: #f0fff0;
    --bg-secondary: #e6ffe6;
}

/* 夏季主题 */
[data-theme="summer"] .multi-order {
    --color-primary: #ffa500;
    --bg-primary: #fffaf0;
    --bg-secondary: #fff8dc;
}

/* 秋季主题 */
[data-theme="autumn"] .multi-order {
    --color-primary: #d2691e;
    --bg-primary: #fdf5e6;
    --bg-secondary: #f5deb3;
}

/* 冬季主题 */
[data-theme="winter"] .multi-order {
    --color-primary: #4682b4;
    --bg-primary: #f0f8ff;
    --bg-secondary: #e6f3ff;
}

/* =================================
   可访问性增强
   ================================= */

/* 大字体支持 */
@media (min-resolution: 2dppx) {
    .multi-order__order-field {
        font-size: calc(var(--font-size-sm) * 1.1);
    }

    .multi-order__field-label,
    .multi-order__field-value {
        font-size: calc(var(--font-size-sm) * 1.1);
    }
}

/* 触摸设备优化 */
@media (pointer: coarse) {
    .multi-order__action-btn {
        width: 44px;
        height: 44px;
        font-size: var(--font-size-sm);
    }

    .multi-order__batch-btn {
        min-height: 44px;
        padding: var(--spacing-3) var(--spacing-4);
    }

    .multi-order__control-btn {
        min-height: 44px;
        padding: var(--spacing-4) var(--spacing-6);
    }

    .multi-order__close-btn {
        width: 44px;
        height: 44px;
    }
}
