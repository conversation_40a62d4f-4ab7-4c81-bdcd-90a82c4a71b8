/**
 * 页面管理器
 * 文件: js/pages/page-manager.js
 * 角色: 页面切换和状态管理，协调主页面和多订单页面的显示
 * 
 * @PAGE_MANAGER 页面管理器
 * 🏷️ 标签: @OTA_PAGE_MANAGER
 * 📝 说明: 负责页面切换逻辑和状态管理
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 页面管理器类
     * 管理不同页面的显示和隐藏
     */
    class PageManager {
        constructor() {
            this.logger = this.getLogger();
            this.router = null;
            
            // 页面配置
            this.config = {
                mainPageId: 'workspace',
                multiOrderPageId: 'multiOrderPanel',
                enableTransitions: true,
                transitionDuration: 300
            };

            // 页面状态
            this.state = {
                currentPage: null,
                previousPage: null,
                isTransitioning: false,
                pageHistory: []
            };

            // 页面元素缓存
            this.elements = {
                mainPage: null,
                multiOrderPage: null
            };

            this.logger.log('📄 页面管理器已初始化', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 初始化页面管理器
         * @param {Object} router - 路由器实例
         */
        init(router) {
            this.router = router;
            
            // 缓存页面元素
            this.cachePageElements();
            
            // 注册路由
            this.registerRoutes();
            
            // 设置初始页面
            this.setInitialPage();
            
            this.logger.log('✅ 页面管理器初始化完成', 'success');
        }

        /**
         * 缓存页面元素
         */
        cachePageElements() {
            this.elements.mainPage = document.getElementById(this.config.mainPageId);

            // 多订单页面现在由 MultiOrder 模块动态创建，不再依赖静态HTML元素
            this.elements.multiOrderPage = document.getElementById(this.config.multiOrderPageId);

            if (!this.elements.mainPage) {
                this.logger.logError('主页面元素未找到', new Error(`Element not found: ${this.config.mainPageId}`));
            }

            // 多订单页面元素不存在是正常的，因为现在由 MultiOrder 模块动态管理
            if (!this.elements.multiOrderPage) {
                this.logger.log('多订单页面使用动态容器（由MultiOrder模块管理）', 'info');
            }

            this.logger.log('📋 页面元素已缓存', 'info');
        }

        /**
         * 注册路由
         */
        registerRoutes() {
            if (!this.router) {
                this.logger.logError('路由器未初始化', new Error('Router not initialized'));
                return;
            }

            // 主页面路由
            this.router.addRoute('/', () => {
                this.showMainPage();
            }, {
                title: 'OTA订单处理系统 - 主页',
                beforeEnter: () => this.canNavigateToMain(),
                afterEnter: () => this.onMainPageEntered()
            });

            // 多订单页面路由已迁移到备份文件夹

            this.logger.log('🛣️ 页面路由已注册', 'info');
        }

        /**
         * 设置初始页面
         */
        setInitialPage() {
            // 默认显示主页面
            this.showMainPage();
            this.state.currentPage = 'main';
        }

        /**
         * 显示主页面
         */
        async showMainPage() {
            if (this.state.isTransitioning) {
                this.logger.log('⏳ 页面切换中，跳过操作', 'warning');
                return;
            }

            try {
                this.state.isTransitioning = true;
                
                this.logger.log('🏠 切换到主页面', 'info');

                // 隐藏多订单页面 - 使用智能状态检查
                if (window.MultiOrder && window.MultiOrder.isVisible()) {
                    // 只有在非多订单处理状态下才隐藏
                    if (!this.isMultiOrderProcessing()) {
                        window.MultiOrder.hideUI();
                        this.logger.log('🚪 已隐藏MultiOrder UI（无活跃订单）', 'info');
                    } else {
                        this.logger.log('🔒 保持MultiOrder UI显示（有活跃多订单处理）', 'info');
                    }
                } else if (window.OTA?.multiOrderPage) {
                    window.OTA.multiOrderPage.hide();
                } else if (this.elements.multiOrderPage) {
                    await this.hidePage(this.elements.multiOrderPage);
                }

                // 显示主页面
                if (this.elements.mainPage) {
                    await this.showPage(this.elements.mainPage);
                }

                // 更新状态
                this.updatePageState('main');

                this.logger.log('✅ 主页面已显示', 'success');

            } catch (error) {
                this.logger.logError('显示主页面失败', error);
            } finally {
                this.state.isTransitioning = false;
            }
        }

        /**
         * 多订单页面功能已迁移到备份文件夹
         */

        /**
         * 显示页面元素
         * @param {HTMLElement} element - 页面元素
         */
        async showPage(element) {
            if (!element) return;

            element.classList.remove('hidden');
            element.style.display = 'flex';
            element.setAttribute('aria-hidden', 'false');

            // 添加过渡效果
            if (this.config.enableTransitions) {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                // 强制重绘
                element.offsetHeight;
                
                element.style.transition = `opacity ${this.config.transitionDuration}ms ease, transform ${this.config.transitionDuration}ms ease`;
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';

                // 等待过渡完成
                await new Promise(resolve => setTimeout(resolve, this.config.transitionDuration));
            }
        }

        /**
         * 隐藏页面元素
         * @param {HTMLElement} element - 页面元素
         */
        async hidePage(element) {
            if (!element) return;

            // 添加过渡效果
            if (this.config.enableTransitions) {
                element.style.transition = `opacity ${this.config.transitionDuration}ms ease, transform ${this.config.transitionDuration}ms ease`;
                element.style.opacity = '0';
                element.style.transform = 'translateY(-20px)';

                // 等待过渡完成
                await new Promise(resolve => setTimeout(resolve, this.config.transitionDuration));
            }

            element.classList.add('hidden');
            element.style.display = 'none';
            element.setAttribute('aria-hidden', 'true');
        }

        /**
         * 更新页面状态
         * @param {string} currentPage - 当前页面
         */
        updatePageState(currentPage) {
            this.state.previousPage = this.state.currentPage;
            this.state.currentPage = currentPage;
            
            // 添加到历史记录
            this.state.pageHistory.push({
                page: currentPage,
                timestamp: Date.now()
            });

            // 限制历史记录长度
            if (this.state.pageHistory.length > 10) {
                this.state.pageHistory = this.state.pageHistory.slice(-10);
            }
        }

        /**
         * 检查是否可以导航到主页面
         * @returns {boolean} 是否可以导航
         */
        canNavigateToMain() {
            // 可以添加权限检查等逻辑
            return true;
        }

        /**
         * 多订单页面导航检查功能已迁移到备份文件夹
         */

        /**
         * 主页面进入后的回调
         */
        onMainPageEntered() {
            // 可以添加主页面特定的初始化逻辑
            this.logger.log('🏠 主页面已激活', 'info');
        }

        /**
         * 多订单页面回调功能已迁移到备份文件夹
         */

        /**
         * 获取当前页面
         * @returns {string} 当前页面标识
         */
        getCurrentPage() {
            return this.state.currentPage;
        }

        /**
         * 获取上一个页面
         * @returns {string} 上一个页面标识
         */
        getPreviousPage() {
            return this.state.previousPage;
        }

        /**
         * 检查是否正在切换页面
         * @returns {boolean} 是否正在切换
         */
        isTransitioning() {
            return this.state.isTransitioning;
        }

        /**
         * 获取页面历史记录
         * @returns {Array} 页面历史记录
         */
        getPageHistory() {
            return [...this.state.pageHistory];
        }

        /**
         * 检查是否正在处理多订单
         * @returns {boolean} true表示有活跃的多订单处理
         */
        isMultiOrderProcessing() {
            try {
                // 🔧 修复：使用MultiOrderIndependent模块替代backup模块
                if (!window.MultiOrderIndependent) {
                    return false; // 如果独立模块不可用，允许隐藏
                }

                // 检查独立模块的状态
                const state = window.MultiOrderIndependent.getState();
                const isUIVisible = state && state.isVisible;
                const hasActiveOrders = state && state.orders && state.orders.length > 0;

                // 当有活跃订单且UI可见时，认为正在处理多订单
                return hasActiveOrders && isUIVisible;

            } catch (error) {
                // 异常情况下的降级策略：不阻止UI隐藏，但记录日志
                this.logger.log('MultiOrderIndependent状态检查异常: ' + error.message, 'warning');
                return false;
            }
        }

        /**
         * 销毁页面管理器
         */
        destroy() {
            // 清理状态
            this.state = {
                currentPage: null,
                previousPage: null,
                isTransitioning: false,
                pageHistory: []
            };

            // 清理元素缓存
            this.elements = {
                mainPage: null,
                multiOrderPage: null
            };

            this.router = null;

            this.logger.log('🗑️ 页面管理器已销毁', 'info');
        }
    }

    // 创建全局页面管理器实例
    const pageManager = new PageManager();

    // 暴露到OTA命名空间
    window.OTA.PageManager = PageManager;
    window.OTA.pageManager = pageManager;

    // 向后兼容
    window.pageManager = pageManager;

    console.log('✅ 页面管理器已加载');

})();
