# CONSENSUS_MultiOrderUIFix

## 明确的需求描述

### 问题根源
**核心问题**: 页面管理器在系统初始化时调用 `setInitialPage()` → `showMainPage()` → `window.MultiOrder.hideUI()`，导致即使多订单检测成功，UI也被立即隐藏。

### 数据流分析
从日志分析确定的实际执行流程：
1. ✅ 系统启动 → 页面管理器初始化 → `setInitialPage()` → `showMainPage()` → `hideUI()`
2. ✅ 用户输入多订单文本 → Gemini解析成功 → 检测到多订单
3. ✅ 实时分析管理器触发 `multiOrderDetected` 事件
4. ✅ MultiOrder模块监听事件 → 调用 `showUI()` 显示面板
5. ❌ **问题**: 某个机制再次调用 `hideUI()` 导致面板被隐藏

### 验收标准
- **功能要求**: 当检测到多订单时，MultiOrder UI必须保持显示状态
- **不破坏**: 单订单处理功能不受影响
- **兼容性**: 现有的E2E测试必须通过

## 技术实现方案

### 根本原因
页面管理器的 `showMainPage()` 函数在初始化和路由切换时无条件隐藏MultiOrder UI：

```javascript
// 当前有问题的逻辑
if (window.MultiOrder && window.MultiOrder.isVisible()) {
    window.MultiOrder.hideUI();  // ← 问题所在
}
```

### 解决方案
**策略**: 修改页面管理器的逻辑，仅在确实需要隐藏多订单UI时才调用 `hideUI()`

**具体实现**:
1. **条件检查**: 添加条件判断，避免在多订单处理过程中强制隐藏UI
2. **状态管理**: 引入多订单处理状态标识
3. **时序控制**: 确保多订单显示不被初始化流程干扰

## 技术约束

### 架构约束
- 必须保持现有的页面管理器架构
- 不能破坏路由系统的正常工作
- 需要与MultiOrder模块的状态管理协调

### 集成方案
- 修改 `js/pages/page-manager.js` 中的 `showMainPage()` 函数
- 添加多订单状态检查逻辑
- 保持向后兼容性

## 任务边界限制

### 修复范围
**文件**: `js/pages/page-manager.js` (第152行)
**函数**: `showMainPage()` 中的MultiOrder隐藏逻辑
**影响**: 仅影响页面切换时的UI隐藏行为

### 不涉及修改
- ✅ Gemini API解析逻辑
- ✅ 多订单检测机制  
- ✅ MultiOrder模块的显示逻辑
- ✅ 事件系统和路由系统
- ✅ 其他页面管理功能

## 验收标准

### 功能验证
1. **多订单检测**: 输入多订单文本后，UI应该显示并保持可见
2. **单订单处理**: 单订单处理不受影响
3. **页面切换**: 主页面切换功能正常工作
4. **状态管理**: MultiOrder UI的显示/隐藏状态正确管理

### 测试要求
- E2E测试必须通过
- 不引入新的回归问题
- 系统启动流程保持稳定

## 实施计划

### 优先级: 高
**原因**: 核心功能缺陷，影响用户使用多订单功能

### 实施步骤
1. **分析**: 确认页面管理器中的具体调用位置 ✅
2. **修复**: 修改 `showMainPage()` 函数的逻辑
3. **测试**: 验证多订单和单订单功能
4. **验证**: 运行E2E测试确保无回归

### 风险评估
**风险等级**: 低
**原因**: 修改范围明确，影响面有限，有明确的验证标准
