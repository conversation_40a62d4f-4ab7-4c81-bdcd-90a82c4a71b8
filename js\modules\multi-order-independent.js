/**
 * ============================================================================
 * 🔢 独立多订单处理模组 - 完全自包含实现
 * ============================================================================
 *
 * @fileoverview 独立多订单处理系统
 * @description 
 * - 独立GoMyHire API调用（无降级机制）
 * - 全屏UI模式（避免路由数据流截断）
 * - 移动端响应式设计
 * - 启动时静态数据同步
 * - 单向历史存储（多订单→历史系统）
 * - 详细错误日志系统
 * - 支持最大30个订单处理
 * - 本地双击启动兼容
 * 
 * @architecture 单文件架构
 * - 职责：完整的多订单处理功能
 * - 原则：自包含，无外部依赖
 * - 接口：全局暴露 window.MultiOrderIndependent
 *
 * @compatibility
 * - ✅ file:// 协议支持
 * - ✅ 移动端响应式
 * - ✅ 零配置部署
 * - ✅ 现代浏览器兼容
 *
 * @version 1.0.0
 * @created 2025-01-22
 */

const MultiOrderIndependent = (function() {
    'use strict';

    // ============================================================================
    // 🔧 核心配置和常量
    // ============================================================================

    const CONFIG = {
        // API配置
        api: {
            baseURL: 'https://api.gomyhire.com',
            timeout: 30000,
            maxRetries: 3,
            retryDelay: 1000,
            rateLimitDelay: 1000
        },
        
        // 订单处理配置
        orders: {
            maxCount: 30,
            batchSize: 5,
            processingDelay: 1000
        },
        
        // UI配置
        ui: {
            animationDuration: 300,
            mobileBreakpoint: 768,
            tabletBreakpoint: 1024
        },
        
        // 日志配置
        logging: {
            enabled: true,
            maxLogs: 1000,
            logLevel: 'info' // debug, info, warn, error
        }
    };

    // ============================================================================
    // 📝 详细日志系统
    // ============================================================================

    class DetailedLogger {
        constructor(module) {
            this.module = module;
            this.logs = [];
            this.startTime = Date.now();
        }

        log(message, level = 'info', data = null) {
            if (!CONFIG.logging.enabled) return;

            const logEntry = {
                timestamp: new Date().toISOString(),
                module: this.module,
                level: level.toUpperCase(),
                message,
                data,
                elapsed: Date.now() - this.startTime
            };

            this.logs.push(logEntry);

            // 限制日志数量
            if (this.logs.length > CONFIG.logging.maxLogs) {
                this.logs.shift();
            }

            // 控制台输出
            const consoleMethod = this.getConsoleMethod(level);
            const prefix = `[${this.module}][${level.toUpperCase()}]`;
            
            if (data) {
                consoleMethod(`${prefix} ${message}`, data);
            } else {
                consoleMethod(`${prefix} ${message}`);
            }
        }

        logAPICall(method, url, requestData, responseData, duration, success) {
            this.log(`API调用: ${method} ${url}`, success ? 'info' : 'error', {
                method,
                url,
                requestData,
                responseData,
                duration,
                success,
                timestamp: new Date().toISOString()
            });
        }

        logError(message, error) {
            this.log(message, 'error', {
                error: error.message,
                stack: error.stack,
                name: error.name
            });
        }

        getConsoleMethod(level) {
            switch (level.toLowerCase()) {
                case 'debug': return console.debug;
                case 'info': return console.info;
                case 'warn': return console.warn;
                case 'error': return console.error;
                default: return console.log;
            }
        }

        getLogs(level = null) {
            if (level) {
                return this.logs.filter(log => log.level === level.toUpperCase());
            }
            return [...this.logs];
        }

        clearLogs() {
            this.logs = [];
        }
    }

    // ============================================================================
    // 🌐 独立GoMyHire API客户端 - 基础框架
    // ============================================================================

    class IndependentGoMyHireAPI {
        constructor() {
            this.logger = new DetailedLogger('GoMyHireAPI');
            this.config = CONFIG.api;
            this.requestCount = 0;
            this.successCount = 0;
            this.errorCount = 0;
            
            this.logger.log('独立GoMyHire API客户端初始化完成');
        }

        /**
         * 获取认证Token
         * @returns {string} 认证Token
         */
        getAuthToken() {
            // 从主项目获取认证token
            const token = window.OTA?.appState?.get('auth')?.token || 
                         window.appState?.get('auth')?.token ||
                         localStorage.getItem('gomyhire_token') ||
                         '';
            
            if (!token) {
                this.logger.log('警告: 未找到认证Token', 'warn');
            }
            
            return token;
        }

        /**
         * 验证订单数据
         * @param {Object} orderData 订单数据
         */
        validateOrderData(orderData) {
            const required = ['customer_name', 'pickup', 'destination'];
            const missing = required.filter(field => !orderData[field]);
            
            if (missing.length > 0) {
                throw new Error(`缺少必填字段: ${missing.join(', ')}`);
            }
        }

        /**
         * 构建订单请求数据
         * @param {Object} orderData 原始订单数据
         * @returns {Object} 格式化的请求数据
         */
        buildOrderRequest(orderData) {
            return {
                customer_name: orderData.customer_name,
                customer_contact: orderData.customer_contact || '',
                pickup: orderData.pickup,
                destination: orderData.destination,
                car_type_id: orderData.car_type_id || null,
                sub_category_id: orderData.sub_category_id || null,
                driving_region_id: orderData.driving_region_id || null,
                language_id: orderData.language_id || null,
                backend_user_id: orderData.backend_user_id || null,
                notes: orderData.notes || '',
                created_at: new Date().toISOString()
            };
        }

        /**
         * 延迟函数
         * @param {number} ms 延迟毫秒数
         * @returns {Promise} Promise对象
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 创建单个订单
         * @param {Object} orderData 订单数据
         * @param {number} orderIndex 订单索引（用于日志）
         * @returns {Promise<Object>} API响应结果
         */
        async createSingleOrder(orderData, orderIndex = 1) {
            const startTime = Date.now();
            this.requestCount++;

            try {
                this.logger.log(`开始创建订单 ${orderIndex}`, 'info', {
                    customerName: orderData.customer_name,
                    pickup: orderData.pickup,
                    destination: orderData.destination
                });

                // 验证订单数据
                this.validateOrderData(orderData);

                // 构建请求
                const requestData = this.buildOrderRequest(orderData);
                const response = await this.makeAPIRequest('/orders', 'POST', requestData);

                const duration = Date.now() - startTime;
                this.successCount++;

                this.logger.logAPICall('POST', '/orders', requestData, response, duration, true);
                this.logger.log(`订单 ${orderIndex} 创建成功`, 'info', {
                    orderId: response.id || response.order_id,
                    duration
                });

                return {
                    success: true,
                    orderId: response.id || response.order_id,
                    response: response,
                    order: orderData,
                    duration
                };

            } catch (error) {
                const duration = Date.now() - startTime;
                this.errorCount++;

                this.logger.logAPICall('POST', '/orders', orderData, null, duration, false);
                this.logger.logError(`订单 ${orderIndex} 创建失败`, error);

                throw new Error(`订单 ${orderIndex} 创建失败: ${error.message}`);
            }
        }

        /**
         * 批量创建订单
         * @param {Array} orders 订单数组
         * @returns {Promise<Object>} 批量处理结果
         */
        async batchCreateOrders(orders) {
            if (!Array.isArray(orders) || orders.length === 0) {
                throw new Error('无效的订单数组');
            }

            if (orders.length > CONFIG.orders.maxCount) {
                throw new Error(`订单数量超限：${orders.length}/${CONFIG.orders.maxCount}`);
            }

            this.logger.log(`开始批量创建订单`, 'info', { orderCount: orders.length });

            const results = [];
            const startTime = Date.now();

            try {
                for (let i = 0; i < orders.length; i++) {
                    const order = orders[i];

                    try {
                        const result = await this.createSingleOrder(order, i + 1);
                        results.push(result);

                        // 添加API调用间隔
                        if (i < orders.length - 1) {
                            await this.delay(this.config.rateLimitDelay);
                        }

                    } catch (error) {
                        results.push({
                            success: false,
                            error: error.message,
                            order: order,
                            orderIndex: i + 1
                        });
                    }
                }

                const totalDuration = Date.now() - startTime;
                const successCount = results.filter(r => r.success).length;
                const errorCount = results.filter(r => !r.success).length;

                this.logger.log('批量订单创建完成', 'info', {
                    total: orders.length,
                    success: successCount,
                    errors: errorCount,
                    duration: totalDuration
                });

                return {
                    success: errorCount === 0,
                    results: results,
                    summary: {
                        total: orders.length,
                        success: successCount,
                        errors: errorCount,
                        duration: totalDuration
                    }
                };

            } catch (error) {
                this.logger.logError('批量订单创建失败', error);
                throw error;
            }
        }

        /**
         * 执行API请求
         * @param {string} endpoint API端点
         * @param {string} method HTTP方法
         * @param {Object} data 请求数据
         * @returns {Promise<Object>} API响应
         */
        async makeAPIRequest(endpoint, method = 'GET', data = null) {
            const url = `${this.config.baseURL}${endpoint}`;
            const token = this.getAuthToken();

            const requestOptions = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };

            // 添加认证头
            if (token) {
                requestOptions.headers['Authorization'] = `Bearer ${token}`;
            }

            // 添加请求体
            if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
                requestOptions.body = JSON.stringify(data);
            }

            // 设置超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
            requestOptions.signal = controller.signal;

            try {
                const response = await fetch(url, requestOptions);
                clearTimeout(timeoutId);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
                }

                return await response.json();

            } catch (error) {
                clearTimeout(timeoutId);

                if (error.name === 'AbortError') {
                    throw new Error(`API请求超时 (${this.config.timeout}ms)`);
                }

                throw error;
            }
        }

        /**
         * 获取API统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                requestCount: this.requestCount,
                successCount: this.successCount,
                errorCount: this.errorCount,
                successRate: this.requestCount > 0 ? (this.successCount / this.requestCount * 100).toFixed(2) + '%' : '0%'
            };
        }
    }

    // ============================================================================
    // 📊 数据处理模组
    // ============================================================================

    class GeminiDataProcessor {
        constructor() {
            this.logger = new DetailedLogger('GeminiDataProcessor');
            this.logger.log('Gemini数据处理器初始化完成');
        }

        /**
         * 处理Gemini返回的多订单数据
         * @param {Object} geminiResult Gemini API返回结果
         * @returns {Object} 处理后的数据
         */
        processGeminiMultiOrderData(geminiResult) {
            try {
                this.logger.log('开始处理Gemini多订单数据', 'info', {
                    hasOrders: !!geminiResult.orders,
                    orderCount: geminiResult.orders?.length || 0
                });

                if (!geminiResult || !geminiResult.orders || !Array.isArray(geminiResult.orders)) {
                    throw new Error('无效的Gemini结果数据');
                }

                if (geminiResult.orders.length > CONFIG.orders.maxCount) {
                    throw new Error(`订单数量超限：${geminiResult.orders.length}/${CONFIG.orders.maxCount}`);
                }

                // 标准化订单数据
                const processedOrders = geminiResult.orders.map((order, index) => ({
                    id: `order_${index + 1}_${Date.now()}`,
                    ...order,
                    selected: true, // 默认全选
                    status: 'ready',
                    timestamp: new Date().toISOString(),
                    processed: true,
                    orderIndex: index + 1
                }));

                const result = {
                    orders: processedOrders,
                    metadata: {
                        source: 'gemini',
                        processedAt: new Date().toISOString(),
                        confidence: geminiResult.confidence || 0.8,
                        originalOrderCount: geminiResult.orders.length,
                        processedOrderCount: processedOrders.length
                    }
                };

                this.logger.log('Gemini数据处理完成', 'info', {
                    processedCount: processedOrders.length,
                    confidence: result.metadata.confidence
                });

                return result;

            } catch (error) {
                this.logger.logError('Gemini数据处理失败', error);
                throw error;
            }
        }

        /**
         * 验证订单数据完整性
         * @param {Array} orders 订单数组
         * @returns {Object} 验证结果
         */
        validateOrders(orders) {
            const validationResult = {
                valid: true,
                errors: [],
                warnings: [],
                validOrders: [],
                invalidOrders: []
            };

            orders.forEach((order, index) => {
                const orderErrors = [];
                const orderWarnings = [];

                // 必填字段检查
                if (!order.customer_name) orderErrors.push('缺少客户姓名');
                if (!order.pickup) orderErrors.push('缺少上车地点');
                if (!order.destination) orderErrors.push('缺少目的地');

                // 可选字段警告
                if (!order.customer_contact) orderWarnings.push('缺少联系方式');
                if (!order.car_type_id) orderWarnings.push('未指定车型');

                if (orderErrors.length > 0) {
                    validationResult.valid = false;
                    validationResult.errors.push(`订单${index + 1}: ${orderErrors.join(', ')}`);
                    validationResult.invalidOrders.push({ ...order, errors: orderErrors });
                } else {
                    validationResult.validOrders.push(order);
                }

                if (orderWarnings.length > 0) {
                    validationResult.warnings.push(`订单${index + 1}: ${orderWarnings.join(', ')}`);
                }
            });

            this.logger.log('订单验证完成', validationResult.valid ? 'info' : 'warn', {
                total: orders.length,
                valid: validationResult.validOrders.length,
                invalid: validationResult.invalidOrders.length,
                warnings: validationResult.warnings.length
            });

            return validationResult;
        }
    }

    class StaticDataSyncer {
        constructor() {
            this.logger = new DetailedLogger('StaticDataSyncer');
            this.cachedData = {};
            this.lastSyncTime = 0;
            this.logger.log('静态数据同步器初始化完成');
        }

        /**
         * 同步主项目的静态数据
         * @returns {Promise<Object>} 静态数据
         */
        async syncStaticData() {
            try {
                this.logger.log('开始同步静态数据');

                const apiService = this.getMainProjectAPIService();
                if (!apiService) {
                    throw new Error('无法获取主项目API服务');
                }

                const staticData = {};

                // 获取车型数据
                try {
                    staticData.carTypes = await apiService.getCarTypes();
                    this.logger.log(`获取车型数据: ${staticData.carTypes.length}条`);
                } catch (error) {
                    this.logger.log('获取车型数据失败', 'warn', { error: error.message });
                    staticData.carTypes = [];
                }

                // 获取服务类型数据
                try {
                    staticData.serviceTypes = await apiService.getSubCategories();
                    this.logger.log(`获取服务类型数据: ${staticData.serviceTypes.length}条`);
                } catch (error) {
                    this.logger.log('获取服务类型数据失败', 'warn', { error: error.message });
                    staticData.serviceTypes = [];
                }

                // 获取区域数据
                try {
                    staticData.regions = await apiService.getDrivingRegions();
                    this.logger.log(`获取区域数据: ${staticData.regions.length}条`);
                } catch (error) {
                    this.logger.log('获取区域数据失败', 'warn', { error: error.message });
                    staticData.regions = [];
                }

                // 获取语言数据
                try {
                    staticData.languages = await apiService.getLanguages();
                    this.logger.log(`获取语言数据: ${staticData.languages.length}条`);
                } catch (error) {
                    this.logger.log('获取语言数据失败', 'warn', { error: error.message });
                    staticData.languages = [];
                }

                // 获取后端用户数据
                try {
                    staticData.backendUsers = await apiService.getBackendUsers();
                    this.logger.log(`获取后端用户数据: ${staticData.backendUsers.length}条`);
                } catch (error) {
                    this.logger.log('获取后端用户数据失败', 'warn', { error: error.message });
                    staticData.backendUsers = [];
                }

                this.cachedData = staticData;
                this.lastSyncTime = Date.now();

                this.logger.log('静态数据同步完成', 'info', {
                    carTypes: staticData.carTypes.length,
                    serviceTypes: staticData.serviceTypes.length,
                    regions: staticData.regions.length,
                    languages: staticData.languages.length,
                    backendUsers: staticData.backendUsers.length
                });

                return staticData;

            } catch (error) {
                this.logger.logError('静态数据同步失败', error);
                throw error;
            }
        }

        /**
         * 获取主项目的API服务
         * @returns {Object|null} API服务实例
         */
        getMainProjectAPIService() {
            return window.OTA?.getService?.('apiService') ||
                   window.OTA?.apiService ||
                   window.apiService ||
                   null;
        }

        /**
         * 获取缓存的静态数据
         * @returns {Object} 静态数据
         */
        getCachedData() {
            return { ...this.cachedData };
        }
    }

    // ============================================================================
    // 🎯 模组状态管理
    // ============================================================================

    let isInitialized = false;
    let currentOrders = [];
    let staticData = {};
    let uiContainer = null;
    let mainLogger = new DetailedLogger('MultiOrderIndependent');
    let apiClient = null;
    let dataProcessor = null;
    let dataSyncer = null;
    let historyIntegrator = null;
    let uiRenderer = null;

    // ============================================================================
    // 📚 历史存储集成器
    // ============================================================================

    class HistoryIntegrator {
        constructor() {
            this.logger = new DetailedLogger('HistoryIntegrator');
            this.logger.log('历史存储集成器初始化完成');
        }

        /**
         * 保存批量订单到主项目历史系统
         * @param {Array} orders 原始订单数组
         * @param {Object} batchResult 批量处理结果
         * @returns {Promise<Object>} 保存结果
         */
        async saveToMainProjectHistory(orders, batchResult) {
            try {
                this.logger.log('开始保存到主项目历史系统', 'info', {
                    orderCount: orders.length,
                    successCount: batchResult.summary.success,
                    errorCount: batchResult.summary.errors
                });

                const historyManager = this.getMainProjectHistoryManager();
                if (!historyManager) {
                    this.logger.log('主项目历史管理器不可用，使用降级存储', 'warn');
                    return await this.fallbackHistorySave(orders, batchResult);
                }

                // 准备历史数据
                const historyData = this.prepareHistoryData(orders, batchResult);

                // 调用主项目历史管理器
                let saveResult;
                if (historyManager.saveMultipleOrders) {
                    saveResult = await historyManager.saveMultipleOrders(historyData.orders, historyData.batchInfo);
                } else if (historyManager.addOrder) {
                    // 逐个保存
                    saveResult = await this.saveOrdersIndividually(historyManager, historyData.orders);
                } else {
                    throw new Error('历史管理器接口不兼容');
                }

                this.logger.log('历史保存完成', 'info', {
                    savedCount: saveResult.savedCount || historyData.orders.length,
                    success: saveResult.success
                });

                return saveResult;

            } catch (error) {
                this.logger.logError('历史保存失败', error);
                // 尝试降级存储
                try {
                    return await this.fallbackHistorySave(orders, batchResult);
                } catch (fallbackError) {
                    this.logger.logError('降级历史保存也失败', fallbackError);
                    throw error;
                }
            }
        }

        /**
         * 准备历史数据
         * @param {Array} orders 订单数组
         * @param {Object} batchResult 批量结果
         * @returns {Object} 格式化的历史数据
         */
        prepareHistoryData(orders, batchResult) {
            const batchId = `multi_order_${Date.now()}`;
            const processedOrders = [];

            batchResult.results.forEach((result, index) => {
                const originalOrder = orders[index];
                const historyOrder = {
                    ...originalOrder,
                    // 添加处理结果信息
                    api_success: result.success,
                    api_order_id: result.orderId || null,
                    api_response: result.response || null,
                    api_error: result.error || null,
                    processing_duration: result.duration || 0,
                    // 添加批量信息
                    batch_id: batchId,
                    batch_index: index,
                    batch_total: orders.length,
                    // 添加时间戳
                    processed_at: new Date().toISOString(),
                    created_via: 'multi_order_independent'
                };

                processedOrders.push(historyOrder);
            });

            return {
                orders: processedOrders,
                batchInfo: {
                    batchId: batchId,
                    totalOrders: orders.length,
                    successCount: batchResult.summary.success,
                    errorCount: batchResult.summary.errors,
                    totalDuration: batchResult.summary.duration,
                    processedAt: new Date().toISOString(),
                    source: 'multi_order_independent'
                }
            };
        }

        /**
         * 逐个保存订单到历史
         * @param {Object} historyManager 历史管理器
         * @param {Array} orders 订单数组
         * @returns {Promise<Object>} 保存结果
         */
        async saveOrdersIndividually(historyManager, orders) {
            let savedCount = 0;
            const errors = [];

            for (let i = 0; i < orders.length; i++) {
                try {
                    await historyManager.addOrder(orders[i], orders[i].id);
                    savedCount++;
                } catch (error) {
                    errors.push(`订单${i + 1}保存失败: ${error.message}`);
                }
            }

            return {
                success: errors.length === 0,
                savedCount: savedCount,
                errors: errors,
                totalOrders: orders.length
            };
        }

        /**
         * 降级历史存储
         * @param {Array} orders 订单数组
         * @param {Object} batchResult 批量结果
         * @returns {Promise<Object>} 保存结果
         */
        async fallbackHistorySave(orders, batchResult) {
            try {
                this.logger.log('使用降级历史存储', 'info');

                const historyKey = 'multi_order_history';
                const existingHistory = JSON.parse(localStorage.getItem(historyKey) || '[]');

                const historyData = this.prepareHistoryData(orders, batchResult);

                // 添加到历史记录开头
                existingHistory.unshift(...historyData.orders);

                // 限制历史记录数量
                const maxHistoryItems = 500;
                if (existingHistory.length > maxHistoryItems) {
                    existingHistory.splice(maxHistoryItems);
                }

                localStorage.setItem(historyKey, JSON.stringify(existingHistory));

                this.logger.log('降级历史存储完成', 'info', {
                    savedCount: historyData.orders.length,
                    totalHistoryItems: existingHistory.length
                });

                return {
                    success: true,
                    savedCount: historyData.orders.length,
                    method: 'localStorage_fallback',
                    totalHistoryItems: existingHistory.length
                };

            } catch (error) {
                this.logger.logError('降级历史存储失败', error);
                throw error;
            }
        }

        /**
         * 获取主项目历史管理器
         * @returns {Object|null} 历史管理器实例
         */
        getMainProjectHistoryManager() {
            return window.OTA?.getService?.('historyManager') ||
                   window.OTA?.historyManager ||
                   window.historyManager ||
                   null;
        }

        /**
         * 获取历史统计信息
         * @returns {Object} 统计信息
         */
        getHistoryStats() {
            try {
                const historyKey = 'multi_order_history';
                const history = JSON.parse(localStorage.getItem(historyKey) || '[]');

                const multiOrderHistory = history.filter(item => item.created_via === 'multi_order_independent');

                return {
                    totalItems: history.length,
                    multiOrderItems: multiOrderHistory.length,
                    lastSaved: multiOrderHistory.length > 0 ? multiOrderHistory[0].processed_at : null
                };
            } catch (error) {
                this.logger.logError('获取历史统计失败', error);
                return {
                    totalItems: 0,
                    multiOrderItems: 0,
                    lastSaved: null
                };
            }
        }
    }

    // ============================================================================
    // 🎨 独立UI渲染器
    // ============================================================================

    class IndependentUIRenderer {
        constructor() {
            this.logger = new DetailedLogger('IndependentUIRenderer');
            this.container = null;
            this.isVisible = false;
            this.eventListeners = new Map();
            this.abortController = new AbortController();

            this.logger.log('独立UI渲染器初始化完成');
        }

        /**
         * 创建UI容器
         */
        createContainer() {
            if (this.container) {
                this.logger.log('UI容器已存在，跳过创建');
                return;
            }

            this.container = document.createElement('div');
            this.container.id = 'multi-order-independent-container';
            this.container.className = 'multi-order-independent';
            this.container.style.display = 'none';

            // 添加样式
            this.attachStyles();

            document.body.appendChild(this.container);
            this.logger.log('UI容器创建完成');
        }

        /**
         * 附加样式
         */
        attachStyles() {
            const styleId = 'multi-order-independent-styles';
            if (document.getElementById(styleId)) {
                return;
            }

            const style = document.createElement('style');
            style.id = styleId;
            style.textContent = this.getStyles();
            document.head.appendChild(style);

            this.logger.log('样式附加完成');
        }

        /**
         * 渲染UI
         * @param {Array} orders 订单数组
         * @param {Object} staticData 静态数据
         */
        render(orders, staticData = {}) {
            if (!this.container) {
                this.createContainer();
            }

            this.logger.log('开始渲染UI', 'info', { orderCount: orders.length });

            const selectedCount = orders.filter(order => order.selected).length;

            this.container.innerHTML = `
                <div class="multi-order-independent__modal">
                    <div class="multi-order-independent__header">
                        <h2 class="multi-order-independent__title">
                            🔢 多订单批量处理
                            <span style="font-weight: 400; font-size: 14px; opacity: 0.9;">
                                (${orders.length} 个订单)
                            </span>
                        </h2>
                        <button class="multi-order-independent__close-btn" onclick="window.MultiOrderIndependent.hideUI()">
                            ✕
                        </button>
                    </div>

                    <div class="multi-order-independent__content">
                        <div class="multi-order-independent__orders-list">
                            ${this.renderOrderCards(orders, staticData)}
                        </div>
                    </div>

                    <div class="multi-order-independent__controls">
                        <div class="multi-order-independent__controls-left">
                            <span style="color: #6c757d; font-size: 14px;">
                                共 ${orders.length} 个订单，已选择 ${selectedCount} 个
                            </span>
                        </div>
                        <div class="multi-order-independent__controls-right">
                            <button class="multi-order-independent__btn" onclick="window.MultiOrderIndependent.selectAllOrders(true)">
                                全选
                            </button>
                            <button class="multi-order-independent__btn" onclick="window.MultiOrderIndependent.selectAllOrders(false)">
                                全不选
                            </button>
                            <button class="multi-order-independent__btn" onclick="window.MultiOrderIndependent.validateAllOrders()">
                                验证订单
                            </button>
                            <button class="multi-order-independent__btn multi-order-independent__btn--primary"
                                    onclick="window.MultiOrderIndependent.processSelectedOrders()"
                                    ${selectedCount === 0 ? 'disabled' : ''}>
                                🚀 批量发送 (${selectedCount})
                            </button>
                        </div>
                    </div>
                </div>
            `;

            this.attachEventListeners();
            this.logger.log('UI渲染完成');
        }

        /**
         * 渲染订单卡片
         * @param {Array} orders 订单数组
         * @param {Object} staticData 静态数据
         * @returns {string} HTML字符串
         */
        renderOrderCards(orders, staticData) {
            return orders.map((order, index) => `
                <div class="multi-order-independent__order-card ${order.selected ? 'selected' : ''} ${order.status || 'ready'}"
                     data-order-id="${order.id}" data-order-index="${index}">

                    <div class="multi-order-independent__order-header">
                        <label class="multi-order-independent__order-checkbox">
                            <input type="checkbox" ${order.selected ? 'checked' : ''}
                                   onchange="window.MultiOrderIndependent.toggleOrderSelection('${order.id}')">
                            订单 ${index + 1}
                        </label>
                        <span class="multi-order-independent__order-status ${order.status || 'ready'}">
                            ${this.getStatusText(order.status || 'ready')}
                        </span>
                    </div>

                    <div class="multi-order-independent__order-fields">
                        <div class="multi-order-independent__field">
                            <label class="multi-order-independent__field-label">客户姓名 *</label>
                            <input type="text" class="multi-order-independent__field-input"
                                   value="${order.customer_name || ''}"
                                   onchange="window.MultiOrderIndependent.updateOrderField('${order.id}', 'customer_name', this.value)"
                                   placeholder="请输入客户姓名">
                        </div>

                        <div class="multi-order-independent__field">
                            <label class="multi-order-independent__field-label">联系电话</label>
                            <input type="text" class="multi-order-independent__field-input"
                                   value="${order.customer_contact || ''}"
                                   onchange="window.MultiOrderIndependent.updateOrderField('${order.id}', 'customer_contact', this.value)"
                                   placeholder="请输入联系电话">
                        </div>

                        <div class="multi-order-independent__field">
                            <label class="multi-order-independent__field-label">上车地点 *</label>
                            <input type="text" class="multi-order-independent__field-input"
                                   value="${order.pickup || ''}"
                                   onchange="window.MultiOrderIndependent.updateOrderField('${order.id}', 'pickup', this.value)"
                                   placeholder="请输入上车地点">
                        </div>

                        <div class="multi-order-independent__field">
                            <label class="multi-order-independent__field-label">目的地 *</label>
                            <input type="text" class="multi-order-independent__field-input"
                                   value="${order.destination || ''}"
                                   onchange="window.MultiOrderIndependent.updateOrderField('${order.id}', 'destination', this.value)"
                                   placeholder="请输入目的地">
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <div class="multi-order-independent__field">
                                <label class="multi-order-independent__field-label">车型</label>
                                <select class="multi-order-independent__field-select"
                                        onchange="window.MultiOrderIndependent.updateOrderField('${order.id}', 'car_type_id', this.value)">
                                    <option value="">请选择车型</option>
                                    ${this.renderSelectOptions(staticData.carTypes || [], order.car_type_id)}
                                </select>
                            </div>

                            <div class="multi-order-independent__field">
                                <label class="multi-order-independent__field-label">服务类型</label>
                                <select class="multi-order-independent__field-select"
                                        onchange="window.MultiOrderIndependent.updateOrderField('${order.id}', 'sub_category_id', this.value)">
                                    <option value="">请选择服务类型</option>
                                    ${this.renderSelectOptions(staticData.serviceTypes || [], order.sub_category_id)}
                                </select>
                            </div>
                        </div>

                        ${order.notes ? `
                            <div class="multi-order-independent__field">
                                <label class="multi-order-independent__field-label">备注</label>
                                <input type="text" class="multi-order-independent__field-input"
                                       value="${order.notes || ''}"
                                       onchange="window.MultiOrderIndependent.updateOrderField('${order.id}', 'notes', this.value)"
                                       placeholder="订单备注">
                            </div>
                        ` : ''}

                        ${order.api_order_id ? `
                            <div class="multi-order-independent__field">
                                <label class="multi-order-independent__field-label">API订单ID</label>
                                <input type="text" class="multi-order-independent__field-input"
                                       value="${order.api_order_id}" readonly
                                       style="background: #f8f9fa; color: #6c757d;">
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }

        /**
         * 渲染下拉选项
         * @param {Array} options 选项数组
         * @param {string} selectedValue 选中值
         * @returns {string} HTML选项字符串
         */
        renderSelectOptions(options, selectedValue) {
            return options.map(option =>
                `<option value="${option.id}" ${option.id == selectedValue ? 'selected' : ''}>
                    ${option.name || option.title || option.label}
                </option>`
            ).join('');
        }

        /**
         * 获取状态文本
         * @param {string} status 状态
         * @returns {string} 状态文本
         */
        getStatusText(status) {
            const statusMap = {
                'ready': '准备就绪',
                'processing': '处理中',
                'success': '创建成功',
                'error': '创建失败'
            };
            return statusMap[status] || status;
        }

        /**
         * 附加事件监听器
         */
        attachEventListeners() {
            const options = { signal: this.abortController.signal };

            // 全局键盘事件
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isVisible) {
                    window.MultiOrderIndependent.hideUI();
                }
            }, options);

            this.logger.log('事件监听器附加完成');
        }

        /**
         * 显示UI
         */
        show() {
            if (!this.container) {
                this.logger.log('UI容器不存在，无法显示', 'warn');
                return;
            }

            this.container.style.display = 'flex';
            this.isVisible = true;

            // 添加显示动画
            requestAnimationFrame(() => {
                this.container.style.opacity = '0';
                this.container.style.transform = 'scale(0.95)';

                requestAnimationFrame(() => {
                    this.container.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    this.container.style.opacity = '1';
                    this.container.style.transform = 'scale(1)';
                });
            });

            this.logger.log('UI显示完成');
        }

        /**
         * 隐藏UI
         */
        hide() {
            if (!this.container) {
                return;
            }

            this.container.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            this.container.style.opacity = '0';
            this.container.style.transform = 'scale(0.95)';

            setTimeout(() => {
                this.container.style.display = 'none';
                this.isVisible = false;
            }, 300);

            this.logger.log('UI隐藏完成');
        }

        /**
         * 更新订单卡片状态
         * @param {string} orderId 订单ID
         * @param {string} status 新状态
         */
        updateOrderStatus(orderId, status) {
            const card = this.container?.querySelector(`[data-order-id="${orderId}"]`);
            if (!card) return;

            // 更新卡片样式
            card.className = `multi-order-independent__order-card ${status}`;

            // 更新状态标签
            const statusElement = card.querySelector('.multi-order-independent__order-status');
            if (statusElement) {
                statusElement.className = `multi-order-independent__order-status ${status}`;
                statusElement.textContent = this.getStatusText(status);
            }
        }

        /**
         * 更新控制面板
         */
        updateControls() {
            const controlsLeft = this.container?.querySelector('.multi-order-independent__controls-left');
            const processBtn = this.container?.querySelector('.multi-order-independent__btn--primary');

            if (!controlsLeft || !processBtn) return;

            const orders = currentOrders;
            const selectedCount = orders.filter(order => order.selected).length;

            controlsLeft.innerHTML = `
                <span style="color: #6c757d; font-size: 14px;">
                    共 ${orders.length} 个订单，已选择 ${selectedCount} 个
                </span>
            `;

            processBtn.disabled = selectedCount === 0;
            processBtn.innerHTML = `🚀 批量发送 (${selectedCount})`;
        }

        /**
         * 销毁UI
         */
        destroy() {
            this.logger.log('开始销毁UI');

            // 取消所有事件监听器
            this.abortController.abort();

            // 移除容器
            if (this.container && this.container.parentNode) {
                this.container.parentNode.removeChild(this.container);
            }

            // 移除样式
            const style = document.getElementById('multi-order-independent-styles');
            if (style && style.parentNode) {
                style.parentNode.removeChild(style);
            }

            this.container = null;
            this.isVisible = false;

            this.logger.log('UI销毁完成');
        }

        /**
         * 获取完整样式
         * @returns {string} CSS样式字符串
         */
        getStyles() {
            return `
                /* 基础容器样式 */
                .multi-order-independent {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100vw;
                    height: 100vh;
                    background: rgba(0, 0, 0, 0.8);
                    z-index: 10000;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    backdrop-filter: blur(4px);
                }

                .multi-order-independent__modal {
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                    width: 95%;
                    max-width: 1200px;
                    max-height: 90vh;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                }

                /* 头部样式 */
                .multi-order-independent__header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px 24px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .multi-order-independent__title {
                    margin: 0;
                    font-size: 18px;
                    font-weight: 600;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .multi-order-independent__close-btn {
                    background: rgba(255, 255, 255, 0.2);
                    border: none;
                    color: white;
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    cursor: pointer;
                    font-size: 18px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: background-color 0.2s;
                }

                .multi-order-independent__close-btn:hover {
                    background: rgba(255, 255, 255, 0.3);
                }

                /* 内容区域 */
                .multi-order-independent__content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                }

                .multi-order-independent__orders-list {
                    flex: 1;
                    overflow-y: auto;
                    padding: 20px;
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
                    gap: 16px;
                    align-content: start;
                }

                /* 订单卡片样式 */
                .multi-order-independent__order-card {
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    padding: 16px;
                    transition: all 0.2s ease;
                    position: relative;
                }

                .multi-order-independent__order-card:hover {
                    border-color: #667eea;
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
                }

                .multi-order-independent__order-card.selected {
                    border-color: #667eea;
                    background: #f0f4ff;
                }

                .multi-order-independent__order-card.processing {
                    border-color: #ffc107;
                    background: #fff8e1;
                }

                .multi-order-independent__order-card.success {
                    border-color: #28a745;
                    background: #f0fff4;
                }

                .multi-order-independent__order-card.error {
                    border-color: #dc3545;
                    background: #fff5f5;
                }

                .multi-order-independent__order-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 12px;
                }

                .multi-order-independent__order-checkbox {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-weight: 600;
                    color: #495057;
                }

                .multi-order-independent__order-checkbox input[type="checkbox"] {
                    width: 18px;
                    height: 18px;
                    cursor: pointer;
                }

                .multi-order-independent__order-status {
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;
                    text-transform: uppercase;
                }

                .multi-order-independent__order-status.ready {
                    background: #e3f2fd;
                    color: #1976d2;
                }

                .multi-order-independent__order-status.processing {
                    background: #fff3e0;
                    color: #f57c00;
                }

                .multi-order-independent__order-status.success {
                    background: #e8f5e8;
                    color: #2e7d32;
                }

                .multi-order-independent__order-status.error {
                    background: #ffebee;
                    color: #c62828;
                }

                /* 订单字段样式 */
                .multi-order-independent__order-fields {
                    display: grid;
                    grid-template-columns: 1fr;
                    gap: 12px;
                }

                .multi-order-independent__field {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }

                .multi-order-independent__field-label {
                    font-size: 12px;
                    font-weight: 500;
                    color: #6c757d;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .multi-order-independent__field-input {
                    padding: 8px 12px;
                    border: 1px solid #ced4da;
                    border-radius: 4px;
                    font-size: 14px;
                    transition: border-color 0.2s;
                }

                .multi-order-independent__field-input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
                }

                .multi-order-independent__field-select {
                    padding: 8px 12px;
                    border: 1px solid #ced4da;
                    border-radius: 4px;
                    font-size: 14px;
                    background: white;
                    cursor: pointer;
                    transition: border-color 0.2s;
                }

                .multi-order-independent__field-select:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
                }

                /* 控制面板样式 */
                .multi-order-independent__controls {
                    padding: 20px 24px;
                    border-top: 1px solid #e9ecef;
                    background: #f8f9fa;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 12px;
                }

                .multi-order-independent__controls-left {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    flex-wrap: wrap;
                }

                .multi-order-independent__controls-right {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    flex-wrap: wrap;
                }

                .multi-order-independent__btn {
                    padding: 10px 16px;
                    border: 1px solid #ced4da;
                    border-radius: 6px;
                    background: white;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 500;
                    transition: all 0.2s;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                }

                .multi-order-independent__btn:hover {
                    background: #f8f9fa;
                    border-color: #adb5bd;
                }

                .multi-order-independent__btn--primary {
                    background: #667eea;
                    color: white;
                    border-color: #667eea;
                }

                .multi-order-independent__btn--primary:hover {
                    background: #5a6fd8;
                    border-color: #5a6fd8;
                }

                .multi-order-independent__btn:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                /* 移动端响应式 */
                @media (max-width: 768px) {
                    .multi-order-independent__modal {
                        width: 100%;
                        height: 100%;
                        max-height: none;
                        border-radius: 0;
                    }

                    .multi-order-independent__orders-list {
                        grid-template-columns: 1fr;
                        padding: 16px;
                    }

                    .multi-order-independent__controls {
                        flex-direction: column;
                        align-items: stretch;
                    }

                    .multi-order-independent__controls-left,
                    .multi-order-independent__controls-right {
                        justify-content: center;
                    }
                }

                @media (max-width: 480px) {
                    .multi-order-independent__header {
                        padding: 16px;
                    }

                    .multi-order-independent__title {
                        font-size: 16px;
                    }

                    .multi-order-independent__order-card {
                        padding: 12px;
                    }

                    .multi-order-independent__btn {
                        padding: 8px 12px;
                        font-size: 13px;
                    }
                }
            `;
        }
    }

    // ============================================================================
    // 🚀 公共API接口
    // ============================================================================

    return {
        // 按需初始化（确保只初始化一次）
        ensureInitialized: async function() {
            if (isInitialized) {
                mainLogger.log('模组已初始化，跳过重复初始化');
                return true;
            }

            // 如果正在初始化，等待完成
            if (this._initializationPromise) {
                return await this._initializationPromise;
            }

            // 开始初始化
            this._initializationPromise = this.initialize();
            
            try {
                const result = await this._initializationPromise;
                mainLogger.log('按需初始化完成');
                return result;
            } catch (error) {
                mainLogger.logError('按需初始化失败', error);
                this._initializationPromise = null; // 重置以允许重试
                throw error;
            } finally {
                this._initializationPromise = null;
            }
        },

        // 初始化
        initialize: async function() {
            if (isInitialized) {
                mainLogger.log('模组已初始化，跳过重复初始化');
                return true;
            }

            try {
                mainLogger.log('开始初始化独立多订单模组');

                // 初始化各个组件
                apiClient = new IndependentGoMyHireAPI();
                dataProcessor = new GeminiDataProcessor();
                dataSyncer = new StaticDataSyncer();
                historyIntegrator = new HistoryIntegrator();
                uiRenderer = new IndependentUIRenderer();

                mainLogger.log('核心组件初始化完成');

                // 同步静态数据
                try {
                    staticData = await dataSyncer.syncStaticData();
                    mainLogger.log('静态数据同步完成', 'info', {
                        carTypes: staticData.carTypes?.length || 0,
                        serviceTypes: staticData.serviceTypes?.length || 0,
                        regions: staticData.regions?.length || 0,
                        languages: staticData.languages?.length || 0
                    });
                } catch (error) {
                    mainLogger.log('静态数据同步失败，将在运行时重试', 'warn', { error: error.message });
                    staticData = {};
                }

                isInitialized = true;
                mainLogger.log('独立多订单模组初始化完成');
                return true;

            } catch (error) {
                mainLogger.logError('模组初始化失败', error);
                return false;
            }
        },

        // 处理Gemini多订单数据
        processGeminiData: function(geminiResult) {
            if (!isInitialized) {
                throw new Error('模组未初始化，请先调用initialize()');
            }

            try {
                const processedData = dataProcessor.processGeminiMultiOrderData(geminiResult);

                // 增强订单数据（添加静态数据选项）
                processedData.orders = processedData.orders.map(order => ({
                    ...order,
                    availableCarTypes: staticData.carTypes || [],
                    availableServiceTypes: staticData.serviceTypes || [],
                    availableRegions: staticData.regions || [],
                    availableLanguages: staticData.languages || [],
                    availableBackendUsers: staticData.backendUsers || []
                }));

                processedData.staticData = staticData;
                currentOrders = processedData.orders;

                mainLogger.log('Gemini数据处理完成', 'info', {
                    orderCount: processedData.orders.length,
                    hasStaticData: Object.keys(staticData).length > 0
                });

                return processedData;

            } catch (error) {
                mainLogger.logError('Gemini数据处理失败', error);
                throw error;
            }
        },

        // 批量处理订单
        processOrders: async function(orders = null) {
            if (!isInitialized) {
                throw new Error('模组未初始化，请先调用initialize()');
            }

            const ordersToProcess = orders || currentOrders.filter(order => order.selected);

            if (ordersToProcess.length === 0) {
                throw new Error('没有选中的订单需要处理');
            }

            try {
                mainLogger.log('开始批量处理订单', 'info', { orderCount: ordersToProcess.length });

                // 验证订单数据
                const validation = dataProcessor.validateOrders(ordersToProcess);
                if (!validation.valid) {
                    throw new Error(`订单验证失败: ${validation.errors.join('; ')}`);
                }

                // 调用API批量创建订单
                const batchResult = await apiClient.batchCreateOrders(ordersToProcess);

                // 保存到历史系统
                try {
                    const historyResult = await historyIntegrator.saveToMainProjectHistory(ordersToProcess, batchResult);
                    mainLogger.log('历史保存完成', 'info', { savedCount: historyResult.savedCount });
                } catch (historyError) {
                    mainLogger.log('历史保存失败，但订单创建成功', 'warn', { error: historyError.message });
                }

                mainLogger.log('批量订单处理完成', 'info', {
                    total: batchResult.summary.total,
                    success: batchResult.summary.success,
                    errors: batchResult.summary.errors
                });

                return batchResult;

            } catch (error) {
                mainLogger.logError('批量订单处理失败', error);
                throw error;
            }
        },

        // 获取状态
        getState: function() {
            return {
                initialized: isInitialized,
                orderCount: currentOrders.length,
                selectedCount: currentOrders.filter(order => order.selected).length,
                hasStaticData: Object.keys(staticData).length > 0,
                apiStats: apiClient ? apiClient.getStats() : null,
                historyStats: historyIntegrator ? historyIntegrator.getHistoryStats() : null
            };
        },

        // 获取当前订单
        getOrders: function() {
            return [...currentOrders];
        },

        // 更新订单
        updateOrder: function(orderId, updates) {
            const orderIndex = currentOrders.findIndex(order => order.id === orderId);
            if (orderIndex === -1) {
                throw new Error(`未找到订单: ${orderId}`);
            }

            currentOrders[orderIndex] = { ...currentOrders[orderIndex], ...updates };
            mainLogger.log('订单更新完成', 'info', { orderId, updates });

            return currentOrders[orderIndex];
        },

        // 选择/取消选择订单
        toggleOrderSelection: function(orderId) {
            const order = currentOrders.find(order => order.id === orderId);
            if (!order) {
                throw new Error(`未找到订单: ${orderId}`);
            }

            order.selected = !order.selected;
            mainLogger.log('订单选择状态更新', 'info', { orderId, selected: order.selected });

            return order.selected;
        },

        // 全选/全不选
        selectAllOrders: function(selected = true) {
            currentOrders.forEach(order => order.selected = selected);
            mainLogger.log(`${selected ? '全选' : '全不选'}订单完成`, 'info', { orderCount: currentOrders.length });

            return currentOrders.filter(order => order.selected).length;
        },

        // 获取日志
        getLogs: function(module = null, level = null) {
            if (module === 'api' && apiClient) {
                return apiClient.logger.getLogs(level);
            }
            if (module === 'data' && dataProcessor) {
                return dataProcessor.logger.getLogs(level);
            }
            if (module === 'sync' && dataSyncer) {
                return dataSyncer.logger.getLogs(level);
            }
            if (module === 'history' && historyIntegrator) {
                return historyIntegrator.logger.getLogs(level);
            }
            return mainLogger.getLogs(level);
        },

        // 显示UI
        showUI: function(orders = null, staticDataOverride = null) {
            if (!isInitialized) {
                throw new Error('模组未初始化，请先调用initialize()');
            }

            const ordersToShow = orders || currentOrders;
            const dataToUse = staticDataOverride || staticData;

            if (ordersToShow.length === 0) {
                throw new Error('没有订单数据可显示');
            }

            try {
                uiRenderer.render(ordersToShow, dataToUse);
                uiRenderer.show();

                mainLogger.log('UI显示完成', 'info', { orderCount: ordersToShow.length });
                return true;

            } catch (error) {
                mainLogger.logError('UI显示失败', error);
                throw error;
            }
        },

        // 隐藏UI
        hideUI: function() {
            if (uiRenderer) {
                uiRenderer.hide();
                mainLogger.log('UI隐藏完成');
            }
        },

        // 更新订单字段
        updateOrderField: function(orderId, field, value) {
            const order = currentOrders.find(order => order.id === orderId);
            if (!order) {
                throw new Error(`未找到订单: ${orderId}`);
            }

            order[field] = value;
            mainLogger.log('订单字段更新完成', 'info', { orderId, field, value });

            return order;
        },

        // 验证所有订单
        validateAllOrders: function() {
            if (!isInitialized || !dataProcessor) {
                throw new Error('模组未初始化');
            }

            try {
                const validation = dataProcessor.validateOrders(currentOrders);

                // 更新UI显示验证结果
                if (uiRenderer && uiRenderer.isVisible) {
                    validation.invalidOrders.forEach(invalidOrder => {
                        const orderId = invalidOrder.id;
                        uiRenderer.updateOrderStatus(orderId, 'error');
                    });
                }

                if (validation.valid) {
                    alert(`✅ 所有订单验证通过！\n有效订单: ${validation.validOrders.length} 个`);
                } else {
                    alert(`❌ 订单验证失败！\n\n错误信息:\n${validation.errors.join('\n')}\n\n警告信息:\n${validation.warnings.join('\n')}`);
                }

                mainLogger.log('订单验证完成', validation.valid ? 'info' : 'warn', {
                    valid: validation.valid,
                    validCount: validation.validOrders.length,
                    invalidCount: validation.invalidOrders.length,
                    warningCount: validation.warnings.length
                });

                return validation;

            } catch (error) {
                mainLogger.logError('订单验证失败', error);
                alert(`验证过程出错: ${error.message}`);
                throw error;
            }
        },

        // 处理选中的订单
        processSelectedOrders: async function() {
            if (!isInitialized) {
                throw new Error('模组未初始化');
            }

            const selectedOrders = currentOrders.filter(order => order.selected);

            if (selectedOrders.length === 0) {
                alert('请至少选择一个订单');
                return;
            }

            if (!confirm(`确定要批量发送 ${selectedOrders.length} 个订单吗？\n\n此操作将调用GoMyHire API创建订单，请确认数据无误。`)) {
                return;
            }

            try {
                // 更新UI状态为处理中
                if (uiRenderer && uiRenderer.isVisible) {
                    selectedOrders.forEach(order => {
                        uiRenderer.updateOrderStatus(order.id, 'processing');
                    });
                }

                mainLogger.log('开始处理选中订单', 'info', { selectedCount: selectedOrders.length });

                // 调用批量处理
                const result = await this.processOrders(selectedOrders);

                // 更新UI状态
                if (uiRenderer && uiRenderer.isVisible) {
                    result.results.forEach((orderResult, index) => {
                        const orderId = selectedOrders[index].id;
                        const status = orderResult.success ? 'success' : 'error';
                        uiRenderer.updateOrderStatus(orderId, status);

                        // 如果成功，更新订单ID
                        if (orderResult.success && orderResult.orderId) {
                            this.updateOrderField(orderId, 'api_order_id', orderResult.orderId);
                        }
                    });

                    // 重新渲染以显示更新的数据
                    setTimeout(() => {
                        uiRenderer.render(currentOrders, staticData);
                    }, 1000);
                }

                // 显示结果
                const successCount = result.summary.success;
                const errorCount = result.summary.errors;

                if (errorCount === 0) {
                    alert(`🎉 批量处理完成！\n\n✅ 成功创建: ${successCount} 个订单\n⏱️ 总耗时: ${Math.round(result.summary.duration / 1000)} 秒`);
                } else {
                    alert(`⚠️ 批量处理完成，但有部分失败\n\n✅ 成功: ${successCount} 个\n❌ 失败: ${errorCount} 个\n⏱️ 总耗时: ${Math.round(result.summary.duration / 1000)} 秒\n\n请检查失败的订单并重试。`);
                }

                return result;

            } catch (error) {
                mainLogger.logError('批量处理失败', error);

                // 重置UI状态
                if (uiRenderer && uiRenderer.isVisible) {
                    selectedOrders.forEach(order => {
                        uiRenderer.updateOrderStatus(order.id, 'error');
                    });
                }

                alert(`❌ 批量处理失败: ${error.message}`);
                throw error;
            }
        },

        // 刷新UI
        refreshUI: function() {
            if (uiRenderer && uiRenderer.isVisible) {
                uiRenderer.render(currentOrders, staticData);
                uiRenderer.updateControls();
                mainLogger.log('UI刷新完成');
            }
        },

        // 清理资源
        destroy: function() {
            mainLogger.log('开始清理模组资源');

            // 销毁UI
            if (uiRenderer) {
                uiRenderer.destroy();
                uiRenderer = null;
            }

            currentOrders = [];
            staticData = {};
            isInitialized = false;

            if (uiContainer && uiContainer.parentNode) {
                uiContainer.parentNode.removeChild(uiContainer);
                uiContainer = null;
            }

            mainLogger.log('模组资源清理完成');
        }
    };
})();

// 全局暴露
window.MultiOrderIndependent = MultiOrderIndependent;

// 项目集成 - 添加到OTA命名空间
if (window.OTA) {
    window.OTA.MultiOrderIndependent = MultiOrderIndependent;
}

// ============================================================================
// 🔗 主程序集成和事件监听
// ============================================================================

(function() {
    'use strict';

    // 等待DOM和主程序加载完成
    function initializeIntegration() {
        try {
            console.log('[MultiOrderIndependent] 开始初始化主程序集成');

            // 初始化模组
            MultiOrderIndependent.initialize().then(success => {
                if (success) {
                    console.log('[MultiOrderIndependent] ✅ 模组初始化成功');
                    setupEventListeners();
                } else {
                    console.error('[MultiOrderIndependent] ❌ 模组初始化失败');
                }
            }).catch(error => {
                console.error('[MultiOrderIndependent] ❌ 模组初始化异常:', error);
            });

        } catch (error) {
            console.error('[MultiOrderIndependent] 集成初始化失败:', error);
        }
    }

    // 设置事件监听器
    function setupEventListeners() {
        try {
            // 监听多订单检测事件
            document.addEventListener('multiOrderDetected', handleMultiOrderDetected);

            // 监听Gemini结果事件（备用）
            document.addEventListener('geminiResultProcessed', handleGeminiResult);

            // 监听页面卸载事件
            window.addEventListener('beforeunload', handlePageUnload);

            console.log('[MultiOrderIndependent] ✅ 事件监听器设置完成');

        } catch (error) {
            console.error('[MultiOrderIndependent] 事件监听器设置失败:', error);
        }
    }

    // 处理多订单检测事件
    async function handleMultiOrderDetected(event) {
        try {
            console.log('[MultiOrderIndependent] 收到多订单检测事件', event.detail);

            const { multiOrderResult, orderText } = event.detail;

            if (!multiOrderResult || !multiOrderResult.isMultiOrder || !multiOrderResult.orders) {
                console.log('[MultiOrderIndependent] 非多订单结果，忽略事件');
                return;
            }

            if (multiOrderResult.orders.length > 30) {
                alert(`❌ 订单数量超限！\n\n检测到 ${multiOrderResult.orders.length} 个订单，但系统最多支持 30 个订单的批量处理。\n\n请分批处理订单。`);
                return;
            }

            console.log('[MultiOrderIndependent] 开始处理多订单数据');

            // 处理Gemini数据
            const processedData = MultiOrderIndependent.processGeminiData(multiOrderResult);

            // 显示UI
            MultiOrderIndependent.showUI(processedData.orders, processedData.staticData);

            console.log('[MultiOrderIndependent] ✅ 多订单模式启动成功', {
                orderCount: processedData.orders.length,
                hasStaticData: !!processedData.staticData
            });

        } catch (error) {
            console.error('[MultiOrderIndependent] 多订单处理失败:', error);
            alert(`❌ 多订单模式启动失败: ${error.message}\n\n请刷新页面重试。`);
        }
    }

    // 处理Gemini结果事件（备用）
    async function handleGeminiResult(event) {
        try {
            const { result } = event.detail;

            // 检查是否是多订单结果
            if (result && result.isMultiOrder && result.orders && result.orders.length >= 2) {
                console.log('[MultiOrderIndependent] 从Gemini结果检测到多订单');

                // 触发多订单检测事件
                const multiOrderEvent = new CustomEvent('multiOrderDetected', {
                    detail: {
                        multiOrderResult: result,
                        orderText: result.originalText || ''
                    }
                });

                document.dispatchEvent(multiOrderEvent);
            }

        } catch (error) {
            console.error('[MultiOrderIndependent] Gemini结果处理失败:', error);
        }
    }

    // 处理页面卸载
    function handlePageUnload() {
        try {
            console.log('[MultiOrderIndependent] 页面卸载，清理资源');
            MultiOrderIndependent.destroy();
        } catch (error) {
            console.error('[MultiOrderIndependent] 资源清理失败:', error);
        }
    }

    // 移除自动初始化，改为按需初始化
    // 多订单模组将在检测到多订单时由GeminiServiceAdapter触发初始化

})();

// ============================================================================
// 🧪 开发调试工具
// ============================================================================

// 仅在开发环境下暴露调试工具
if (window.location.protocol === 'file:' || window.location.hostname === 'localhost') {
    window.MultiOrderIndependentDebug = {
        // 模拟多订单检测事件
        simulateMultiOrderDetection: function(orderCount = 3) {
            const mockOrders = [];
            for (let i = 1; i <= orderCount; i++) {
                mockOrders.push({
                    customer_name: `测试客户${i}`,
                    customer_contact: `1380000000${i}`,
                    pickup: `上车地点${i}`,
                    destination: `目的地${i}`,
                    notes: `测试订单${i}`
                });
            }

            const mockResult = {
                isMultiOrder: true,
                orders: mockOrders,
                confidence: 0.9,
                orderCount: orderCount
            };

            const event = new CustomEvent('multiOrderDetected', {
                detail: {
                    multiOrderResult: mockResult,
                    orderText: `模拟的${orderCount}个订单文本`
                }
            });

            document.dispatchEvent(event);
            console.log('[Debug] 模拟多订单检测事件已触发', mockResult);
        },

        // 获取模组状态
        getState: () => MultiOrderIndependent.getState(),

        // 获取日志
        getLogs: (module, level) => MultiOrderIndependent.getLogs(module, level),

        // 显示测试UI
        showTestUI: function() {
            this.simulateMultiOrderDetection(3);
        }
    };

    console.log('[MultiOrderIndependent] 🧪 调试工具已加载');
    console.log('使用 window.MultiOrderIndependentDebug.showTestUI() 测试UI');
}
