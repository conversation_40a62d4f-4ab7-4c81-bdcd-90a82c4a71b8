# BEM命名规范映射表

## 命名规范说明

### BEM结构
- **Block**: `.multi-order` (根组件)
- **Element**: `.multi-order__element` (组件内的元素)
- **Modifier**: `.multi-order__element--modifier` (元素的状态或变体)

### 命名原则
1. 使用双下划线 `__` 分隔Block和Element
2. 使用双连字符 `--` 分隔Element和Modifier
3. 使用连字符 `-` 连接多个单词
4. 保持语义化和可读性

## 完整映射表

### 根容器和布局
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.multi-order-unified-container` | `.multi-order` | 根容器 |
| `.multi-order-header` | `.multi-order__header` | 头部区域 |
| `.multi-order-content` | `.multi-order__content` | 主内容区域 |
| `.orders-list` | `.multi-order__orders-list` | 订单列表容器 |
| `.status` | `.multi-order__status` | 状态信息 |

### 头部组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.header-icon` | `.multi-order__header-icon` | 头部图标 |
| `.header-actions` | `.multi-order__header-actions` | 头部操作区 |
| `.order-counter` | `.multi-order__order-counter` | 订单计数器 |
| `.close-btn` | `.multi-order__close-btn` | 关闭按钮 |

### 订单项组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.order-item` | `.multi-order__order-item` | 订单项 |
| `.order-item.selected` | `.multi-order__order-item--selected` | 选中状态 |
| `.order-header` | `.multi-order__order-header` | 订单头部 |
| `.order-content` | `.multi-order__order-content` | 订单内容 |
| `.order-checkbox` | `.multi-order__order-checkbox` | 订单复选框 |

### 订单字段组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.order-fields` | `.multi-order__order-fields` | 字段容器 |
| `.order-field` | `.multi-order__order-field` | 单个字段 |
| `.field-label` | `.multi-order__field-label` | 字段标签 |
| `.field-value` | `.multi-order__field-value` | 字段值 |
| `.editable-field` | `.multi-order__field--editable` | 可编辑字段 |
| `.field-input` | `.multi-order__field-input` | 字段输入框 |

### 订单状态组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.order-status` | `.multi-order__order-status` | 订单状态 |
| `.status-label` | `.multi-order__status-label` | 状态标签 |
| `.status-value` | `.multi-order__status-value` | 状态值 |
| `.status-detected` | `.multi-order__status-value--detected` | 检测状态 |
| `.status-ready` | `.multi-order__status-value--ready` | 就绪状态 |
| `.status-processing` | `.multi-order__status-value--processing` | 处理中状态 |
| `.status-completed` | `.multi-order__status-value--completed` | 完成状态 |
| `.status-error` | `.multi-order__status-value--error` | 错误状态 |
| `.status-failed` | `.multi-order__status-value--failed` | 失败状态 |
| `.status-pending` | `.multi-order__status-value--pending` | 待处理状态 |

### 控制按钮组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.controls` | `.multi-order__controls` | 控制按钮区域 |
| `.controls button` | `.multi-order__control-btn` | 控制按钮 |
| `.controls button:first-child` | `.multi-order__control-btn--primary` | 主要按钮 |
| `.controls button:nth-child(2)` | `.multi-order__control-btn--secondary` | 次要按钮 |
| `.controls button:last-child` | `.multi-order__control-btn--cancel` | 取消按钮 |

### 批量操作组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.batch-operations-bar` | `.multi-order__batch-operations` | 批量操作栏 |
| `.batch-selection` | `.multi-order__batch-selection` | 批量选择区 |
| `.batch-actions` | `.multi-order__batch-actions` | 批量操作区 |
| `.batch-btn` | `.multi-order__batch-btn` | 批量操作按钮 |
| `.batch-btn.primary` | `.multi-order__batch-btn--primary` | 主要批量按钮 |
| `.selection-count` | `.multi-order__selection-count` | 选择计数 |

### 状态栏组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.status-bar` | `.multi-order__status-bar` | 状态栏 |
| `.status-info` | `.multi-order__status-info` | 状态信息 |
| `.status-text` | `.multi-order__status-text` | 状态文本 |
| `.progress-indicator` | `.multi-order__progress-indicator` | 进度指示器 |
| `.progress-bar` | `.multi-order__progress-bar` | 进度条 |
| `.progress-fill` | `.multi-order__progress-fill` | 进度填充 |
| `.progress-text` | `.multi-order__progress-text` | 进度文本 |

### 订单操作组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.order-title` | `.multi-order__order-title` | 订单标题 |
| `.order-number` | `.multi-order__order-number` | 订单号 |
| `.order-id` | `.multi-order__order-id` | 订单ID |
| `.order-actions` | `.multi-order__order-actions` | 订单操作 |
| `.action-btn` | `.multi-order__action-btn` | 操作按钮 |
| `.action-btn.edit-btn` | `.multi-order__action-btn--edit` | 编辑按钮 |
| `.action-btn.duplicate-btn` | `.multi-order__action-btn--duplicate` | 复制按钮 |
| `.action-btn.delete-btn` | `.multi-order__action-btn--delete` | 删除按钮 |

### 模态框组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.order-edit-modal` | `.multi-order__modal` | 编辑模态框 |
| `.modal-overlay` | `.multi-order__modal-overlay` | 模态框遮罩 |
| `.modal-content` | `.multi-order__modal-content` | 模态框内容 |
| `.modal-header` | `.multi-order__modal-header` | 模态框头部 |
| `.modal-body` | `.multi-order__modal-body` | 模态框主体 |
| `.modal-footer` | `.multi-order__modal-footer` | 模态框底部 |
| `.modal-close` | `.multi-order__modal-close` | 关闭按钮 |

### 表单组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.edit-form` | `.multi-order__form` | 编辑表单 |
| `.form-group` | `.multi-order__form-group` | 表单组 |
| `.form-row` | `.multi-order__form-row` | 表单行 |

### 状态操作组件
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.status-actions` | `.multi-order__status-actions` | 状态操作 |
| `.status-btn` | `.multi-order__status-btn` | 状态按钮 |

### 特殊字段修饰符
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `.order-field[data-field="团号"]` | `.multi-order__order-field--group-number` | 团号字段 |
| `.order-field[data-field="客人"]` | `.multi-order__order-field--guest` | 客人字段 |
| `.order-field[data-field="联系"]` | `.multi-order__order-field--contact` | 联系字段 |
| `.order-field[data-field="时间"]` | `.multi-order__order-field--time` | 时间字段 |
| `.order-field[data-field="日期"]` | `.multi-order__order-field--date` | 日期字段 |
| `.order-field[data-field="起点"]` | `.multi-order__order-field--origin` | 起点字段 |
| `.order-field[data-field="终点"]` | `.multi-order__order-field--destination` | 终点字段 |
| `.order-field[data-field="路线"]` | `.multi-order__order-field--route` | 路线字段 |

### 主题修饰符
| 当前类名 | 新BEM类名 | 说明 |
|---------|-----------|------|
| `[data-theme="dark"] .multi-order-*` | `.multi-order--dark` | 暗色主题 |
| `.multi-order-module.processing` | `.multi-order--processing` | 处理中状态 |

## 使用示例

### HTML结构示例
```html
<div class="multi-order multi-order--processing">
    <header class="multi-order__header">
        <h3 class="multi-order__header-title">
            <span class="multi-order__header-icon">📋</span>
            多订单管理
        </h3>
        <div class="multi-order__header-actions">
            <span class="multi-order__order-counter">5</span>
            <button class="multi-order__close-btn">×</button>
        </div>
    </header>
    
    <main class="multi-order__content">
        <div class="multi-order__orders-list">
            <article class="multi-order__order-item multi-order__order-item--selected">
                <div class="multi-order__order-fields">
                    <div class="multi-order__order-field multi-order__order-field--group-number">
                        <span class="multi-order__field-label">团号:</span>
                        <span class="multi-order__field-value">ABC123</span>
                    </div>
                </div>
            </article>
        </div>
    </main>
</div>
```

### CSS使用示例
```css
.multi-order {
    /* 根容器样式 */
}

.multi-order__order-item {
    /* 订单项基础样式 */
}

.multi-order__order-item--selected {
    /* 选中状态样式 */
}

.multi-order__order-field--group-number {
    /* 团号字段特殊样式 */
}
```
